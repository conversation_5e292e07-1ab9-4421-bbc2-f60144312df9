# PostgreSQL Command Finder

A powerful, modular web application that helps users find PostgreSQL commands through an intuitive question-based navigation system.

## Features

- **Interactive Navigation**: Navigate through categories and subcategories to find specific commands
- **Detailed Descriptions**: Each command includes comprehensive explanations and parameter details
- **Modular Architecture**: Easily extensible command system organized by responsibility
- **Parameter Guidance**: Commands with parameters include detailed explanations for each flag/option
- **Responsive Design**: Clean, modern UI that works across devices

## Architecture

The application uses a modular command system for maximum extensibility:

```
commands/
├── index.js           # Central registry and validation
├── installation.js    # Installation & Server Management
├── databases.js       # Database & User Management
├── data-interaction.js # Data manipulation & inspection
├── backup-restore.js  # Backup & Restore operations
└── performance.js     # Performance monitoring & maintenance
```

### Adding New Commands

To add new commands, follow these steps:

1. **Choose the appropriate module** based on the command's category
2. **Add the command object** to the relevant array:

```javascript
{
  path: ["Category Name", "Subcategory", "Command Name"],
  command: "actual command syntax",
  description: "Detailed explanation of what the command does",
  parameters: [
    {
      flag: "-U",
      name: "username",
      description: "Description of this parameter"
    }
    // ... more parameters
  ]
}
```

3. **Run validation** to ensure the command structure is correct
4. **Test the navigation** to the new command

### Example: Adding a New Backup Command

```javascript
// In commands/backup-restore.js
{
  path: ["Backup and Restore", "Advanced Options", "Parallel Backup"],
  command: "pg_dump -U username -d database -j 4 -F d -f backup_directory",
  description: "Create a parallel backup using multiple jobs for faster processing.",
  parameters: [
    { flag: "-U", name: "username", description: "PostgreSQL user with backup privileges" },
    { flag: "-d", name: "database", description: "Database to back up" },
    { flag: "-j", name: "jobs", description: "Number of parallel jobs (4 in this example)" },
    { flag: "-F d", name: "format", description: "Directory format for parallel backups" },
    { flag: "-f", name: "output", description: "Output directory path" }
  ]
}
```

### Adding New Categories

To add entirely new categories:

1. **Create a new module file** (e.g., `commands/security.js`)
2. **Export the commands array** and validation function
3. **Import in `commands/index.js`** and add to the registry
4. **Update the path arrays** in your command objects

### Validation System

Each module includes a validation function that ensures:
- All commands have required fields (path, command, description)
- Commands belong to the correct category
- Path arrays are properly structured

Run validation in browser console:
```javascript
import { validateAllCommands } from './commands/index.js';
console.log(validateAllCommands());
```

## Usage

1. Open `index.html` in a modern web browser
2. Navigate through the questions by clicking on options
3. Reach specific commands with detailed explanations
4. Use the "Back" button to explore different paths

## Development

### File Structure
- `index.html`: Main HTML structure
- `style.css`: Application styling
- `script.js`: Main application logic and tree building
- `commands/`: Modular command definitions

### Browser Compatibility
- Modern browsers with ES6 module support
- Tested on Chrome, Firefox, Safari, Edge

### Run
run the following command in the root directory of the project:

```batch
python3 -m http.server 8000
```

## Contributing

When contributing new commands:
1. Follow the existing code style and structure
2. Include comprehensive descriptions
3. Add parameter details for complex commands
4. Test navigation to ensure proper tree building
5. Run validation to catch structural issues

## Future Enhancements

Potential improvements for the system:
- Search functionality across all commands
- Command usage statistics
- Export functionality (copy commands to clipboard)
- Command favoriting/bookmarking
- Integration with PostgreSQL documentation
- Multi-language support
- Dark/light theme toggle