body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f4f4f4;
  color: #333;
}

#app {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 30px;
}

#question {
  font-size: 1.4em;
  font-weight: bold;
  margin-bottom: 20px;
  color: #34495e;
}

#choices button {
  display: block;
  width: 100%;
  padding: 15px;
  margin: 10px 0;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1em;
  cursor: pointer;
  transition: background 0.3s ease;
}

#choices button:hover {
  background: linear-gradient(135deg, #2980b9, #21618c);
  transform: translateY(-2px);
}

#command-display {
  background: #ecf0f1;
  padding: 20px;
  border-radius: 5px;
  margin-top: 20px;
}

#command-display h2 {
  color: #2c3e50;
  margin-top: 0;
}

#command {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 15px;
  border-radius: 5px;
  font-family: 'Courier New', monospace;
  overflow-x: auto;
  white-space: pre-wrap;
}

#description {
  margin: 15px 0;
  line-height: 1.6;
}

#back {
  background: #e74c3c;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1em;
  transition: background 0.3s ease;
}

#back:hover {
  background: #c0392b;
}