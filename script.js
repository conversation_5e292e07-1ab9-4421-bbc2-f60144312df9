// Import all commands from the modular command system
import { allCommands, validateAllCommands, commandSystemMetadata } from './commands/index.js';

// Function to build the tree from the commands data
function buildTree(commands) {
  const root = { question: "What do you need help with in PostgreSQL?", children: [] };

  commands.forEach(cmd => {
    let current = root;
    // Create intermediate nodes for all path segments except the last one
    for (let i = 0; i < cmd.path.length - 1; i++) {
      const segment = cmd.path[i];
      let child = current.children.find(c => c.question === segment);
      if (!child) {
        child = { question: segment, children: [] };
        current.children.push(child);
      }
      current = child;
    }
    // Add the command at the leaf with the last path segment as text
    current.children.push({
      text: cmd.path[cmd.path.length - 1],
      command: cmd.command,
      description: cmd.description,
      parameters: cmd.parameters || []
    });
  });

  return root;
}

const tree = buildTree(allCommands);

let currentNode = tree;
let path = [tree];

function displayBreadcrumbs() {
  const breadcrumbsDiv = document.getElementById('breadcrumbs');
  breadcrumbsDiv.innerHTML = '';

  if (path.length <= 1) {
    breadcrumbsDiv.style.display = 'none';
    return;
  }

  breadcrumbsDiv.style.display = 'block';

  // Add home button
  const homeLink = document.createElement('button');
  homeLink.textContent = '🏠 Home';
  homeLink.className = 'breadcrumb-link';
  homeLink.onclick = () => {
    path = [tree];
    currentNode = tree;
    displayQuestion();
  };
  breadcrumbsDiv.appendChild(homeLink);

  // Add separator
  const separator1 = document.createElement('span');
  separator1.textContent = ' > ';
  separator1.className = 'breadcrumb-separator';
  breadcrumbsDiv.appendChild(separator1);

  // Add breadcrumb links for each level
  path.slice(1).forEach((node, index) => {
    const link = document.createElement('button');
    link.textContent = node.question || node.text;
    link.className = 'breadcrumb-link';
    link.onclick = () => {
      // Jump to this level in the path
      const newLength = index + 2; // +2 because slice(1) and we want up to this index
      path = path.slice(0, newLength);
      currentNode = path[path.length - 1];
      displayQuestion();
    };
    breadcrumbsDiv.appendChild(link);

    // Add separator if not the last one
    if (index < path.slice(1).length - 1) {
      const separator = document.createElement('span');
      separator.textContent = ' > ';
      separator.className = 'breadcrumb-separator';
      breadcrumbsDiv.appendChild(separator);
    }
  });
}

function displayQuestion() {
  document.getElementById('command-display').style.display = 'none';
  document.getElementById('question').style.display = 'block';
  document.getElementById('choices').style.display = 'block';
  document.getElementById('question').textContent = currentNode.question;
  displayBreadcrumbs();
  const choicesDiv = document.getElementById('choices');
  choicesDiv.innerHTML = '';
  currentNode.children.forEach(child => {
    const button = document.createElement('button');
    button.textContent = child.text || child.question;
    button.onclick = () => {
      path.push(child);
      currentNode = child;
      if (child.command) {
        showCommand(child);
      } else {
        displayQuestion();
      }
    };
    choicesDiv.appendChild(button);
  });
}

function showCommand(node) {
  document.getElementById('question').style.display = 'none';
  document.getElementById('choices').style.display = 'none';
  document.getElementById('command-display').style.display = 'block';
  displayBreadcrumbs();
  document.getElementById('command').textContent = node.command;
  document.getElementById('description').textContent = node.description;
  const paramsDiv = document.getElementById('parameters');
  const paramsList = document.getElementById('parameters-list');
  if (node.parameters && node.parameters.length > 0) {
    paramsDiv.style.display = 'block';
    paramsList.innerHTML = '';
    node.parameters.forEach(param => {
      const li = document.createElement('li');
      li.innerHTML = `<strong>${param.flag}</strong> (${param.name}): ${param.description}`;
      paramsList.appendChild(li);
    });
  } else {
    paramsDiv.style.display = 'none';
  }
}

document.getElementById('back').onclick = () => {
  if (path.length > 1) {
    path.pop();
    currentNode = path[path.length - 1];
    displayQuestion();
  }
};

document.getElementById('home').onclick = () => {
  path = [tree];
  currentNode = tree;
  displayQuestion();
};

displayQuestion();