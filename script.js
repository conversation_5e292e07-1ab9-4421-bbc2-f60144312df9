// Import all commands from the modular command system
import { allCommands, validateAllCommands, commandSystemMetadata } from './commands/index.js';

// Function to build the tree from the commands data
function buildTree(commands) {
  const root = { question: "What do you need help with in PostgreSQL?", children: [] };

  commands.forEach(cmd => {
    let current = root;
    cmd.path.forEach((segment, index) => {
      let child = current.children.find(c => c.question === segment);
      if (!child) {
        child = { question: segment, children: [] };
        current.children.push(child);
      }
      current = child;
    });
    // Add the command at the leaf
    current.children.push({
      text: cmd.path[cmd.path.length - 1],
      command: cmd.command,
      description: cmd.description,
      parameters: cmd.parameters || []
    });
  });

  return root;
}

const tree = buildTree(allCommands);

let currentNode = tree;
let path = [tree];

function displayQuestion() {
  document.getElementById('command-display').style.display = 'none';
  document.getElementById('question').style.display = 'block';
  document.getElementById('choices').style.display = 'block';
  document.getElementById('question').textContent = currentNode.question;
  const choicesDiv = document.getElementById('choices');
  choicesDiv.innerHTML = '';
  currentNode.children.forEach(child => {
    const button = document.createElement('button');
    button.textContent = child.text || child.question;
    button.onclick = () => {
      path.push(child);
      currentNode = child;
      if (child.command) {
        showCommand(child);
      } else {
        displayQuestion();
      }
    };
    choicesDiv.appendChild(button);
  });
}

function showCommand(node) {
  document.getElementById('question').style.display = 'none';
  document.getElementById('choices').style.display = 'none';
  document.getElementById('command-display').style.display = 'block';
  document.getElementById('command').textContent = node.command;
  document.getElementById('description').textContent = node.description;
  const paramsDiv = document.getElementById('parameters');
  const paramsList = document.getElementById('parameters-list');
  if (node.parameters && node.parameters.length > 0) {
    paramsDiv.style.display = 'block';
    paramsList.innerHTML = '';
    node.parameters.forEach(param => {
      const li = document.createElement('li');
      li.innerHTML = `<strong>${param.flag}</strong> (${param.name}): ${param.description}`;
      paramsList.appendChild(li);
    });
  } else {
    paramsDiv.style.display = 'none';
  }
}

document.getElementById('back').onclick = () => {
  if (path.length > 1) {
    path.pop();
    currentNode = path[path.length - 1];
    displayQuestion();
  }
};

displayQuestion();