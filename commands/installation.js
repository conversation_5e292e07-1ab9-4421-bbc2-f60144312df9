// Installation and Server Management Commands
export const installationCommands = [
  {
    path: ["Installation and Server Management", "Installing PostgreSQL", "Using Homebrew", "Install Homebrew"],
    command: `/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"`,
    description: "Homebrew is a popular package manager for macOS that simplifies the installation of software like PostgreSQL. Run this command in your terminal to install Homebrew."
  },
  {
    path: ["Installation and Server Management", "Installing PostgreSQL", "Using Homebrew", "Install PostgreSQL"],
    command: "brew install postgresql",
    description: "Once Homebrew is installed, use this command to download and install the latest stable version of PostgreSQL along with any necessary dependencies."
  },
  {
    path: ["Installation and Server Management", "Installing PostgreSQL", "Using Postgres.app"],
    command: "Download and install Postgres.app from postgresapp.com",
    description: "Postgres.app is a full-featured PostgreSQL installation packaged as a standard Mac application. Download it from the official website, move it to your Applications folder, and double-click to launch. The app provides a menu bar icon for easy server management."
  },
  {
    path: ["Installation and Server Management", "Managing the Server", "Start PostgreSQL Server"],
    command: "brew services start postgresql",
    description: "Start the PostgreSQL server and have it run in the background."
  },
  {
    path: ["Installation and Server Management", "Managing the Server", "Stop PostgreSQL Server"],
    command: "brew services stop postgresql",
    description: "Stop the PostgreSQL server."
  },
  {
    path: ["Installation and Server Management", "Managing the Server", "Restart PostgreSQL Server"],
    command: "brew services restart postgresql",
    description: "Restart the PostgreSQL server, useful after changing configuration files."
  },
  {
    path: ["Installation and Server Management", "Managing the Server", "Check Server Status"],
    command: "brew services list",
    description: "Check the current status of the PostgreSQL service and other Homebrew services."
  },
  {
    path: ["Installation and Server Management", "Connect to PostgreSQL"],
    command: "psql",
    description: "Connect to the PostgreSQL interactive terminal (psql) using the default database and user. This is the primary way to interact with PostgreSQL.",
    parameters: [
      { flag: "psql", name: "default connection", description: "Connect with default settings (current user, default database)" },
      { flag: "-U username", name: "username", description: "Connect as specific user" },
      { flag: "-d database", name: "database", description: "Connect to specific database" },
      { flag: "-h hostname", name: "hostname", description: "Connect to remote host" },
      { flag: "-p port", name: "port", description: "Connect to specific port (default: 5432)" },
      { flag: "-W", name: "password prompt", description: "Force password prompt" }
    ]
  },
  {
    path: ["Installation and Server Management", "Connection Methods", "Connect with Full Parameters"],
    command: "psql -h localhost -p 5432 -U myuser -d mydatabase",
    description: "Connect to PostgreSQL with specific host, port, user, and database parameters.",
    parameters: [
      { flag: "-h", name: "host", description: "Database server host (localhost, IP address, or hostname)" },
      { flag: "-p", name: "port", description: "Port number (default: 5432)" },
      { flag: "-U", name: "username", description: "PostgreSQL username to connect as" },
      { flag: "-d", name: "database", description: "Database name to connect to" },
      { flag: "-W", name: "password", description: "Prompt for password" },
      { flag: "-w", name: "no password", description: "Never prompt for password" }
    ]
  },
  {
    path: ["Installation and Server Management", "Connection Methods", "Connect with Connection String"],
    command: "psql '********************************************/database'",
    description: "Connect using a PostgreSQL connection URI/string format.",
    parameters: [
      { flag: "postgresql://", name: "protocol", description: "PostgreSQL connection protocol" },
      { flag: "username:password", name: "credentials", description: "Username and password (password optional)" },
      { flag: "@hostname:5432", name: "server", description: "Host and port information" },
      { flag: "/database", name: "database", description: "Database name to connect to" },
      { flag: "?sslmode=require", name: "ssl mode", description: "SSL connection requirements" }
    ]
  },
  {
    path: ["Installation and Server Management", "Connection Methods", "Connect via Environment Variables"],
    command: "export PGHOST=localhost PGPORT=5432 PGUSER=myuser PGDATABASE=mydatabase && psql",
    description: "Set PostgreSQL environment variables and connect without specifying parameters.",
    parameters: [
      { flag: "PGHOST", name: "pg host", description: "Default host for connections" },
      { flag: "PGPORT", name: "pg port", description: "Default port for connections" },
      { flag: "PGUSER", name: "pg user", description: "Default username for connections" },
      { flag: "PGDATABASE", name: "pg database", description: "Default database for connections" },
      { flag: "PGPASSWORD", name: "pg password", description: "Default password (not recommended for security)" },
      { flag: "PGSSLMODE", name: "pg ssl mode", description: "SSL connection mode (disable, allow, prefer, require)" }
    ]
  },
  {
    path: ["Installation and Server Management", "Configuration Files", "Locate Configuration Files"],
    command: "SHOW config_file; SHOW hba_file; SHOW ident_file;",
    description: "Find the locations of PostgreSQL configuration files.",
    parameters: [
      { flag: "SHOW config_file", name: "config file", description: "Location of postgresql.conf" },
      { flag: "SHOW hba_file", name: "hba file", description: "Location of pg_hba.conf (authentication)" },
      { flag: "SHOW ident_file", name: "ident file", description: "Location of pg_ident.conf (user mapping)" },
      { flag: "SHOW data_directory", name: "data directory", description: "Location of PostgreSQL data directory" }
    ]
  },
  {
    path: ["Installation and Server Management", "Configuration Files", "Edit postgresql.conf"],
    command: "sudo nano /usr/local/var/postgres/postgresql.conf",
    description: "Edit the main PostgreSQL configuration file. Path may vary by installation method.",
    parameters: [
      { flag: "sudo", name: "superuser", description: "May need superuser privileges to edit" },
      { flag: "nano", name: "text editor", description: "Text editor (nano, vim, emacs, etc.)" },
      { flag: "/usr/local/var/postgres/", name: "homebrew path", description: "Typical path for Homebrew installation" },
      { flag: "/var/lib/postgresql/data/", name: "linux path", description: "Typical path for Linux installations" },
      { flag: "postgresql.conf", name: "config file", description: "Main configuration file" }
    ]
  },
  {
    path: ["Installation and Server Management", "Configuration Files", "Edit pg_hba.conf"],
    command: "sudo nano /usr/local/var/postgres/pg_hba.conf",
    description: "Edit the host-based authentication configuration file to control client access.",
    parameters: [
      { flag: "pg_hba.conf", name: "hba config", description: "Host-based authentication configuration" },
      { flag: "local", name: "local connections", description: "Unix domain socket connections" },
      { flag: "host", name: "host connections", description: "TCP/IP connections (encrypted or not)" },
      { flag: "hostssl", name: "ssl connections", description: "TCP/IP connections with SSL encryption" },
      { flag: "trust", name: "trust auth", description: "Allow connection without password" },
      { flag: "md5", name: "md5 auth", description: "Require MD5-encrypted password" },
      { flag: "scram-sha-256", name: "scram auth", description: "Require SCRAM-SHA-256 authentication" }
    ]
  },
  {
    path: ["Installation and Server Management", "Environment Setup", "Create .pgpass File"],
    command: "echo 'hostname:port:database:username:password' >> ~/.pgpass && chmod 600 ~/.pgpass",
    description: "Create a password file to avoid password prompts for automated scripts.",
    parameters: [
      { flag: "~/.pgpass", name: "pgpass file", description: "Password file in user's home directory" },
      { flag: "hostname:port:database:username:password", name: "entry format", description: "Format for each line in .pgpass" },
      { flag: "chmod 600", name: "file permissions", description: "Set secure permissions (owner read/write only)" },
      { flag: "*", name: "wildcard", description: "Use * to match any hostname, port, database, or username" }
    ]
  },
  {
    path: ["Installation and Server Management", "Environment Setup", "Setup PostgreSQL Service (systemd)"],
    command: "sudo systemctl enable postgresql && sudo systemctl start postgresql",
    description: "Enable and start PostgreSQL service on Linux systems using systemd.",
    parameters: [
      { flag: "systemctl enable", name: "enable service", description: "Enable service to start automatically on boot" },
      { flag: "systemctl start", name: "start service", description: "Start the service immediately" },
      { flag: "systemctl status", name: "check status", description: "Check service status" },
      { flag: "systemctl restart", name: "restart service", description: "Restart the service" },
      { flag: "systemctl stop", name: "stop service", description: "Stop the service" }
    ]
  }
];

// Validation function for this module
export function validateInstallationCommands() {
  return installationCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Installation and Server Management"
  );
}