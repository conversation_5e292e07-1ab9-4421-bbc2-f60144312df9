// Installation and Server Management Commands
export const installationCommands = [
  {
    path: ["Installation and Server Management", "Installing PostgreSQL", "Using Homebrew", "Install Homebrew"],
    command: `/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"`,
    description: "Homebrew is a popular package manager for macOS that simplifies the installation of software like PostgreSQL. Run this command in your terminal to install Homebrew."
  },
  {
    path: ["Installation and Server Management", "Installing PostgreSQL", "Using Homebrew", "Install PostgreSQL"],
    command: "brew install postgresql",
    description: "Once Homebrew is installed, use this command to download and install the latest stable version of PostgreSQL along with any necessary dependencies."
  },
  {
    path: ["Installation and Server Management", "Installing PostgreSQL", "Using Postgres.app"],
    command: "Download and install Postgres.app from postgresapp.com",
    description: "Postgres.app is a full-featured PostgreSQL installation packaged as a standard Mac application. Download it from the official website, move it to your Applications folder, and double-click to launch. The app provides a menu bar icon for easy server management."
  },
  {
    path: ["Installation and Server Management", "Managing the Server", "Start PostgreSQL Server"],
    command: "brew services start postgresql",
    description: "Start the PostgreSQL server and have it run in the background."
  },
  {
    path: ["Installation and Server Management", "Managing the Server", "Stop PostgreSQL Server"],
    command: "brew services stop postgresql",
    description: "Stop the PostgreSQL server."
  },
  {
    path: ["Installation and Server Management", "Managing the Server", "Restart PostgreSQL Server"],
    command: "brew services restart postgresql",
    description: "Restart the PostgreSQL server, useful after changing configuration files."
  },
  {
    path: ["Installation and Server Management", "Managing the Server", "Check Server Status"],
    command: "brew services list",
    description: "Check the current status of the PostgreSQL service and other Homebrew services."
  },
  {
    path: ["Installation and Server Management", "Connect to PostgreSQL"],
    command: "psql",
    description: "Connect to the PostgreSQL interactive terminal (psql) using the default database and user. This is the primary way to interact with PostgreSQL."
  }
];

// Validation function for this module
export function validateInstallationCommands() {
  return installationCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Installation and Server Management"
  );
}