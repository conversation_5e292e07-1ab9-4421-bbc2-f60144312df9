// Security and Authentication Commands
export const securityCommands = [
  {
    path: ["Security and Authentication", "SSL Configuration", "Enable SSL"],
    command: "ALTER SYSTEM SET ssl = on;",
    description: "Enable SSL encryption for database connections. Requires server restart and SSL certificates.",
    parameters: [
      { flag: "ALTER SYSTEM", name: "alter system", description: "Modify server configuration parameters" },
      { flag: "SET ssl = on", name: "enable ssl", description: "Turn on SSL encryption" },
      { flag: "SELECT pg_reload_conf()", name: "reload config", description: "Reload configuration without restart (for some settings)" }
    ]
  },
  {
    path: ["Security and Authentication", "SSL Configuration", "Check SSL Status"],
    command: "SHOW ssl;",
    description: "Check if SSL is currently enabled on the server.",
    parameters: [
      { flag: "SHOW ssl", name: "show ssl", description: "Display current SSL setting" },
      { flag: "SELECT * FROM pg_stat_ssl", name: "ssl stats", description: "View SSL statistics for active connections" }
    ]
  },
  {
    path: ["Security and Authentication", "Authentication Methods", "View pg_hba.conf Rules"],
    command: "SELECT * FROM pg_hba_file_rules;",
    description: "Display the current host-based authentication rules from pg_hba.conf.",
    parameters: [
      { flag: "pg_hba_file_rules", name: "hba rules view", description: "System view showing parsed pg_hba.conf entries" },
      { flag: "line_number", name: "line number", description: "Line number in the pg_hba.conf file" },
      { flag: "type", name: "connection type", description: "Connection type (local, host, hostssl, hostnossl)" },
      { flag: "database", name: "database", description: "Database name or 'all'" },
      { flag: "user_name", name: "user name", description: "User name or 'all'" },
      { flag: "auth_method", name: "auth method", description: "Authentication method (md5, trust, peer, etc.)" }
    ]
  },
  {
    path: ["Security and Authentication", "Password Security", "Set Password Policy"],
    command: "ALTER SYSTEM SET password_encryption = 'scram-sha-256';",
    description: "Configure password encryption method. SCRAM-SHA-256 is more secure than MD5.",
    parameters: [
      { flag: "password_encryption", name: "encryption method", description: "Password hashing algorithm" },
      { flag: "scram-sha-256", name: "scram-sha-256", description: "Most secure option (PostgreSQL 10+)" },
      { flag: "md5", name: "md5", description: "Legacy option, less secure" },
      { flag: "plain", name: "plain", description: "No encryption (not recommended)" }
    ]
  },
  {
    path: ["Security and Authentication", "Password Security", "Force Password Change"],
    command: "ALTER USER username PASSWORD 'new_password' VALID UNTIL '2024-12-31';",
    description: "Set a new password with an expiration date to force periodic password changes.",
    parameters: [
      { flag: "ALTER USER", name: "alter user", description: "Modify user properties" },
      { flag: "PASSWORD", name: "password", description: "Set new password" },
      { flag: "VALID UNTIL", name: "expiration", description: "Password expiration date" },
      { flag: "ENCRYPTED", name: "encrypted", description: "Specify that password is already encrypted" },
      { flag: "UNENCRYPTED", name: "unencrypted", description: "Specify that password should be encrypted by server" }
    ]
  },
  {
    path: ["Security and Authentication", "Access Control", "Audit Login Attempts"],
    command: "SELECT usename, datname, client_addr, backend_start, state FROM pg_stat_activity;",
    description: "Monitor current database connections to audit access patterns.",
    parameters: [
      { flag: "usename", name: "username", description: "Name of the connected user" },
      { flag: "datname", name: "database name", description: "Database being accessed" },
      { flag: "client_addr", name: "client address", description: "IP address of the client" },
      { flag: "backend_start", name: "connection start", description: "When the connection was established" },
      { flag: "state", name: "connection state", description: "Current state of the connection" }
    ]
  },
  {
    path: ["Security and Authentication", "Access Control", "Grant Connect Privilege"],
    command: "GRANT CONNECT ON DATABASE sensitive_db TO trusted_user;",
    description: "Grant connection privileges to specific databases for fine-grained access control.",
    parameters: [
      { flag: "GRANT CONNECT", name: "grant connect", description: "Allow user to connect to database" },
      { flag: "ON DATABASE", name: "on database", description: "Specify the target database" },
      { flag: "TO trusted_user", name: "to user", description: "User receiving the privilege" },
      { flag: "WITH GRANT OPTION", name: "with grant option", description: "Allow user to grant this privilege to others" }
    ]
  },
  {
    path: ["Security and Authentication", "Data Encryption", "Enable Transparent Data Encryption"],
    command: "-- TDE requires PostgreSQL with encryption extensions",
    description: "Transparent Data Encryption (TDE) encrypts data at rest. Requires special PostgreSQL builds or extensions.",
    parameters: [
      { flag: "pg_tde", name: "tde extension", description: "Third-party extension for transparent data encryption" },
      { flag: "encryption_key_command", name: "key command", description: "Command to retrieve encryption keys" },
      { flag: "cluster_passphrase_command", name: "passphrase command", description: "Command for cluster-wide encryption passphrase" }
    ]
  },
  {
    path: ["Security and Authentication", "Data Encryption", "Column-Level Encryption"],
    command: "SELECT pgp_sym_encrypt('sensitive data', 'encryption_key') as encrypted_data;",
    description: "Encrypt specific columns using the pgcrypto extension for sensitive data protection.",
    parameters: [
      { flag: "pgp_sym_encrypt()", name: "symmetric encrypt", description: "Encrypt data with a symmetric key" },
      { flag: "pgp_sym_decrypt()", name: "symmetric decrypt", description: "Decrypt data with the same key" },
      { flag: "pgp_pub_encrypt()", name: "public key encrypt", description: "Encrypt with public key cryptography" },
      { flag: "digest()", name: "hash function", description: "Create hash of data (one-way)" }
    ]
  },
  {
    path: ["Security and Authentication", "Security Auditing", "Enable Audit Logging"],
    command: "ALTER SYSTEM SET log_statement = 'all';",
    description: "Configure PostgreSQL to log all SQL statements for security auditing.",
    parameters: [
      { flag: "log_statement", name: "log statement", description: "What statements to log (none, ddl, mod, all)" },
      { flag: "log_connections", name: "log connections", description: "Log connection attempts" },
      { flag: "log_disconnections", name: "log disconnections", description: "Log when connections end" },
      { flag: "log_line_prefix", name: "log prefix", description: "Information to include at start of each log line" },
      { flag: "log_destination", name: "log destination", description: "Where to send log output (stderr, csvlog, syslog)" }
    ]
  },
  {
    path: ["Security and Authentication", "Security Auditing", "View Failed Login Attempts"],
    command: "-- Check PostgreSQL logs for FATAL authentication failures",
    description: "Monitor log files for failed authentication attempts to detect potential security threats.",
    parameters: [
      { flag: "log_min_messages", name: "log level", description: "Minimum message level to log" },
      { flag: "FATAL", name: "fatal errors", description: "Authentication failures are logged as FATAL" },
      { flag: "grep 'authentication failed'", name: "search logs", description: "Command to search log files for auth failures" },
      { flag: "log_rotation", name: "log rotation", description: "Configure automatic log file rotation" }
    ]
  }
];

// Validation function for this module
export function validateSecurityCommands() {
  return securityCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Security and Authentication"
  );
}
