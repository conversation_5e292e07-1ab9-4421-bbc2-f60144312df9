// Interacting with Data Commands
export const dataInteractionCommands = [
  {
    path: ["Interacting with Data", "Inspecting Your Database", "List Tables"],
    command: "\\dt",
    description: "List all tables in the current database. Use \\dt+ for more details including size and description."
  },
  {
    path: ["Interacting with Data", "Inspecting Your Database", "Describe a Table"],
    command: "\\d your_table_name",
    description: "Show the columns, data types, and constraints of a specific table."
  },
  {
    path: ["Interacting with Data", "Inspecting Your Database", "List Schemas"],
    command: "\\dn",
    description: "List all schemas in the current database. A schema is a namespace containing database objects."
  },
  {
    path: ["Interacting with Data", "Inspecting Your Database", "List Views"],
    command: "\\dv",
    description: "List all views in the current database."
  },
  {
    path: ["Interacting with Data", "Inspecting Your Database", "List Functions"],
    command: "\\df",
    description: "List all functions in the current database."
  },
  {
    path: ["Interacting with Data", "Inspecting Your Database", "List Users and Roles"],
    command: "\\du",
    description: "List all users and roles (same as in the Managing Users section)."
  },
  {
    path: ["Interacting with Data", "Inspecting Your Database", "Get Help on psql Commands"],
    command: "\\?",
    description: "Show help for all psql meta-commands. Use \\h followed by a SQL command for help on SQL syntax."
  },
  {
    path: ["Interacting with Data", "Defining Data Structures", "Create a Table"],
    command: `CREATE TABLE employees (
  id SERIAL PRIMARY KEY,
  first_name VARCHAR(50),
  last_name VARCHAR(50),
  email VARCHAR(100) UNIQUE,
  hire_date DATE DEFAULT CURRENT_DATE
);`,
    description: "Create a new table with various column types and constraints. SERIAL creates an auto-incrementing integer, PRIMARY KEY makes id unique, UNIQUE ensures email uniqueness."
  },
  {
    path: ["Interacting with Data", "Defining Data Structures", "Alter a Table", "Add a Column"],
    command: "ALTER TABLE employees ADD COLUMN phone_number VARCHAR(20);",
    description: "Add a new column to an existing table."
  },
  {
    path: ["Interacting with Data", "Defining Data Structures", "Alter a Table", "Remove a Column"],
    command: "ALTER TABLE employees DROP COLUMN phone_number;",
    description: "Permanently remove a column from a table."
  },
  {
    path: ["Interacting with Data", "Defining Data Structures", "Alter a Table", "Rename a Column"],
    command: "ALTER TABLE employees RENAME COLUMN email TO email_address;",
    description: "Change the name of an existing column."
  },
  {
    path: ["Interacting with Data", "Defining Data Structures", "Alter a Table", "Change Column Data Type"],
    command: "ALTER TABLE employees ALTER COLUMN first_name TYPE VARCHAR(100);",
    description: "Modify the data type of an existing column."
  },
  {
    path: ["Interacting with Data", "Defining Data Structures", "Delete a Table"],
    command: "DROP TABLE employees;",
    description: "Permanently delete a table and all its data."
  },
  {
    path: ["Interacting with Data", "Manipulating Data", "Insert Data"],
    command: "INSERT INTO employees (first_name, last_name, email_address) VALUES ('John', 'Doe', '<EMAIL>');",
    description: "Add new rows to a table. You can omit columns with default values."
  },
  {
    path: ["Interacting with Data", "Manipulating Data", "Query Data", "Select All Columns from All Rows"],
    command: "SELECT * FROM employees;",
    description: "Retrieve all data from a table."
  },
  {
    path: ["Interacting with Data", "Manipulating Data", "Query Data", "Select Specific Columns"],
    command: "SELECT first_name, email_address FROM employees;",
    description: "Retrieve only specific columns from a table."
  },
  {
    path: ["Interacting with Data", "Manipulating Data", "Query Data", "Select with a Filter"],
    command: "SELECT * FROM employees WHERE last_name = 'Doe';",
    description: "Retrieve rows that match specific criteria."
  },
  {
    path: ["Interacting with Data", "Manipulating Data", "Update Data"],
    command: "UPDATE employees SET email_address = '<EMAIL>' WHERE id = 1;",
    description: "Modify existing rows. Always use WHERE to avoid updating all rows."
  },
  {
    path: ["Interacting with Data", "Manipulating Data", "Delete Data"],
    command: "DELETE FROM employees WHERE id = 1;",
    description: "Remove rows from a table. Always use WHERE to avoid deleting all rows."
  },
  {
    path: ["Interacting with Data", "Exit psql"],
    command: "\\q",
    description: "Exit the psql interactive terminal. You can also press Ctrl+D."
  }
];

// Validation function for this module
export function validateDataInteractionCommands() {
  return dataInteractionCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Interacting with Data"
  );
}