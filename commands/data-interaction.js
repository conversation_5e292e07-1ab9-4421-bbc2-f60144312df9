// Interacting with Data Commands
export const dataInteractionCommands = [
  {
    path: ["Interacting with Data", "Inspecting Your Database", "List Tables"],
    command: "\\dt",
    description: "List all tables in the current database. Use \\dt+ for more details including size and description."
  },
  {
    path: ["Interacting with Data", "Inspecting Your Database", "Describe a Table"],
    command: "\\d your_table_name",
    description: "Show the columns, data types, and constraints of a specific table."
  },
  {
    path: ["Interacting with Data", "Inspecting Your Database", "List Schemas"],
    command: "\\dn",
    description: "List all schemas in the current database. A schema is a namespace containing database objects."
  },
  {
    path: ["Interacting with Data", "Inspecting Your Database", "List Views"],
    command: "\\dv",
    description: "List all views in the current database."
  },
  {
    path: ["Interacting with Data", "Inspecting Your Database", "List Functions"],
    command: "\\df",
    description: "List all functions in the current database."
  },
  {
    path: ["Interacting with Data", "Inspecting Your Database", "List Users and Roles"],
    command: "\\du",
    description: "List all users and roles (same as in the Managing Users section)."
  },
  {
    path: ["Interacting with Data", "Inspecting Your Database", "Get Help on psql Commands"],
    command: "\\?",
    description: "Show help for all psql meta-commands. Use \\h followed by a SQL command for help on SQL syntax."
  },
  {
    path: ["Interacting with Data", "Defining Data Structures", "Create a Table"],
    command: `CREATE TABLE employees (
  id SERIAL PRIMARY KEY,
  first_name VARCHAR(50),
  last_name VARCHAR(50),
  email VARCHAR(100) UNIQUE,
  hire_date DATE DEFAULT CURRENT_DATE
);`,
    description: "Create a new table with various column types and constraints. SERIAL creates an auto-incrementing integer, PRIMARY KEY makes id unique, UNIQUE ensures email uniqueness."
  },
  {
    path: ["Interacting with Data", "Defining Data Structures", "Alter a Table", "Add a Column"],
    command: "ALTER TABLE employees ADD COLUMN phone_number VARCHAR(20);",
    description: "Add a new column to an existing table."
  },
  {
    path: ["Interacting with Data", "Defining Data Structures", "Alter a Table", "Remove a Column"],
    command: "ALTER TABLE employees DROP COLUMN phone_number;",
    description: "Permanently remove a column from a table."
  },
  {
    path: ["Interacting with Data", "Defining Data Structures", "Alter a Table", "Rename a Column"],
    command: "ALTER TABLE employees RENAME COLUMN email TO email_address;",
    description: "Change the name of an existing column."
  },
  {
    path: ["Interacting with Data", "Defining Data Structures", "Alter a Table", "Change Column Data Type"],
    command: "ALTER TABLE employees ALTER COLUMN first_name TYPE VARCHAR(100);",
    description: "Modify the data type of an existing column."
  },
  {
    path: ["Interacting with Data", "Defining Data Structures", "Delete a Table"],
    command: "DROP TABLE employees;",
    description: "Permanently delete a table and all its data."
  },
  {
    path: ["Interacting with Data", "Manipulating Data", "Insert Data"],
    command: "INSERT INTO employees (first_name, last_name, email_address) VALUES ('John', 'Doe', '<EMAIL>');",
    description: "Add new rows to a table. You can omit columns with default values."
  },
  {
    path: ["Interacting with Data", "Manipulating Data", "Query Data", "Select All Columns from All Rows"],
    command: "SELECT * FROM employees;",
    description: "Retrieve all data from a table."
  },
  {
    path: ["Interacting with Data", "Manipulating Data", "Query Data", "Select Specific Columns"],
    command: "SELECT first_name, email_address FROM employees;",
    description: "Retrieve only specific columns from a table."
  },
  {
    path: ["Interacting with Data", "Manipulating Data", "Query Data", "Select with a Filter"],
    command: "SELECT * FROM employees WHERE last_name = 'Doe';",
    description: "Retrieve rows that match specific criteria."
  },
  {
    path: ["Interacting with Data", "Manipulating Data", "Update Data"],
    command: "UPDATE employees SET email_address = '<EMAIL>' WHERE id = 1;",
    description: "Modify existing rows. Always use WHERE to avoid updating all rows."
  },
  {
    path: ["Interacting with Data", "Manipulating Data", "Delete Data"],
    command: "DELETE FROM employees WHERE id = 1;",
    description: "Remove rows from a table. Always use WHERE to avoid deleting all rows."
  },
  {
    path: ["Interacting with Data", "Exit psql"],
    command: "\\q",
    description: "Exit the psql interactive terminal. You can also press Ctrl+D.",
    parameters: [
      { flag: "\\q", name: "quit", description: "Exit psql immediately" },
      { flag: "Ctrl+D", name: "EOF signal", description: "Alternative way to exit psql using keyboard shortcut" }
    ]
  },
  {
    path: ["Interacting with Data", "Index Management", "Create Index"],
    command: "CREATE INDEX idx_employee_email ON employees(email);",
    description: "Create an index on a table column to improve query performance for searches and joins.",
    parameters: [
      { flag: "CREATE INDEX", name: "create index", description: "Command to create a new index" },
      { flag: "idx_employee_email", name: "index name", description: "Name for the index (optional but recommended)" },
      { flag: "ON employees", name: "table", description: "Table to create the index on" },
      { flag: "(email)", name: "columns", description: "Column(s) to index (can be multiple columns)" },
      { flag: "UNIQUE", name: "unique", description: "Create a unique index (prevents duplicate values)" },
      { flag: "CONCURRENTLY", name: "concurrent", description: "Create index without locking the table (slower but non-blocking)" }
    ]
  },
  {
    path: ["Interacting with Data", "Index Management", "List Indexes"],
    command: "\\di",
    description: "List all indexes in the current database. Use \\di+ for detailed information including size.",
    parameters: [
      { flag: "\\di", name: "list indexes", description: "Basic index listing" },
      { flag: "\\di+", name: "detailed list", description: "Extended listing with size and description" },
      { flag: "\\di table_name", name: "table indexes", description: "List indexes for a specific table" },
      { flag: "\\di pattern", name: "pattern match", description: "List indexes matching a pattern" }
    ]
  },
  {
    path: ["Interacting with Data", "Index Management", "Drop Index"],
    command: "DROP INDEX idx_employee_email;",
    description: "Remove an index from the database. Use CONCURRENTLY to avoid locking the table.",
    parameters: [
      { flag: "DROP INDEX", name: "drop index", description: "Command to remove an index" },
      { flag: "idx_employee_email", name: "index name", description: "Name of the index to drop" },
      { flag: "CONCURRENTLY", name: "concurrent", description: "Drop index without locking the table" },
      { flag: "IF EXISTS", name: "if exists", description: "Don't error if the index doesn't exist" },
      { flag: "CASCADE", name: "cascade", description: "Drop dependent objects as well" }
    ]
  },
  {
    path: ["Interacting with Data", "Index Management", "Reindex"],
    command: "REINDEX INDEX idx_employee_email;",
    description: "Rebuild an index to improve performance or fix corruption.",
    parameters: [
      { flag: "REINDEX INDEX", name: "reindex index", description: "Rebuild a specific index" },
      { flag: "REINDEX TABLE", name: "reindex table", description: "Rebuild all indexes on a table" },
      { flag: "REINDEX DATABASE", name: "reindex database", description: "Rebuild all indexes in the database" },
      { flag: "CONCURRENTLY", name: "concurrent", description: "Reindex without locking (PostgreSQL 12+)" }
    ]
  },
  {
    path: ["Interacting with Data", "Constraint Management", "Add Primary Key"],
    command: "ALTER TABLE employees ADD CONSTRAINT pk_employees PRIMARY KEY (id);",
    description: "Add a primary key constraint to an existing table.",
    parameters: [
      { flag: "ALTER TABLE", name: "alter table", description: "Command to modify table structure" },
      { flag: "ADD CONSTRAINT", name: "add constraint", description: "Add a new constraint to the table" },
      { flag: "pk_employees", name: "constraint name", description: "Name for the constraint" },
      { flag: "PRIMARY KEY", name: "primary key", description: "Type of constraint - ensures uniqueness and not null" },
      { flag: "(id)", name: "columns", description: "Column(s) that make up the primary key" }
    ]
  },
  {
    path: ["Interacting with Data", "Constraint Management", "Add Foreign Key"],
    command: "ALTER TABLE orders ADD CONSTRAINT fk_customer FOREIGN KEY (customer_id) REFERENCES customers(id);",
    description: "Add a foreign key constraint to enforce referential integrity between tables.",
    parameters: [
      { flag: "ALTER TABLE", name: "alter table", description: "Command to modify table structure" },
      { flag: "ADD CONSTRAINT", name: "add constraint", description: "Add a new constraint" },
      { flag: "fk_customer", name: "constraint name", description: "Name for the foreign key constraint" },
      { flag: "FOREIGN KEY", name: "foreign key", description: "Type of constraint - references another table" },
      { flag: "(customer_id)", name: "local columns", description: "Column(s) in this table" },
      { flag: "REFERENCES customers(id)", name: "references", description: "Referenced table and column(s)" },
      { flag: "ON DELETE CASCADE", name: "on delete", description: "Action when referenced row is deleted" },
      { flag: "ON UPDATE CASCADE", name: "on update", description: "Action when referenced row is updated" }
    ]
  },
  {
    path: ["Interacting with Data", "Constraint Management", "Add Check Constraint"],
    command: "ALTER TABLE employees ADD CONSTRAINT chk_salary CHECK (salary > 0);",
    description: "Add a check constraint to enforce data validation rules.",
    parameters: [
      { flag: "ALTER TABLE", name: "alter table", description: "Command to modify table structure" },
      { flag: "ADD CONSTRAINT", name: "add constraint", description: "Add a new constraint" },
      { flag: "chk_salary", name: "constraint name", description: "Name for the check constraint" },
      { flag: "CHECK", name: "check constraint", description: "Type of constraint - validates data with a condition" },
      { flag: "(salary > 0)", name: "condition", description: "Boolean expression that must be true" }
    ]
  },
  {
    path: ["Interacting with Data", "Advanced SQL", "Common Table Expressions (CTE)"],
    command: "WITH recent_orders AS (SELECT * FROM orders WHERE order_date > '2023-01-01') SELECT * FROM recent_orders;",
    description: "Use Common Table Expressions to create temporary named result sets for complex queries.",
    parameters: [
      { flag: "WITH", name: "with clause", description: "Keyword to start a CTE definition" },
      { flag: "recent_orders", name: "CTE name", description: "Name for the temporary result set" },
      { flag: "AS (SELECT...)", name: "CTE definition", description: "Query that defines the CTE" },
      { flag: "RECURSIVE", name: "recursive", description: "Create a recursive CTE for hierarchical data" }
    ]
  },
  {
    path: ["Interacting with Data", "Advanced SQL", "Window Functions"],
    command: "SELECT name, salary, RANK() OVER (ORDER BY salary DESC) as salary_rank FROM employees;",
    description: "Use window functions to perform calculations across related rows without grouping.",
    parameters: [
      { flag: "RANK()", name: "window function", description: "Window function (RANK, ROW_NUMBER, LAG, LEAD, etc.)" },
      { flag: "OVER", name: "over clause", description: "Defines the window for the function" },
      { flag: "ORDER BY", name: "order by", description: "Ordering within the window" },
      { flag: "PARTITION BY", name: "partition by", description: "Divide rows into groups for separate calculations" }
    ]
  },
  {
    path: ["Interacting with Data", "Advanced SQL", "JSON Operations"],
    command: "SELECT data->>'name' as name, data->'address'->>'city' as city FROM users WHERE data ? 'email';",
    description: "Query and manipulate JSON data stored in PostgreSQL columns.",
    parameters: [
      { flag: "->", name: "json operator", description: "Get JSON object field as JSON" },
      { flag: "->>", name: "json text operator", description: "Get JSON object field as text" },
      { flag: "?", name: "exists operator", description: "Check if JSON object has a key" },
      { flag: "@>", name: "contains operator", description: "Check if JSON contains another JSON" },
      { flag: "jsonb_path_query()", name: "path query", description: "Query JSON using JSONPath expressions" }
    ]
  },
  {
    path: ["Interacting with Data", "Stored Procedures and Functions", "Create Function"],
    command: `CREATE OR REPLACE FUNCTION calculate_bonus(salary NUMERIC)
RETURNS NUMERIC AS $$
BEGIN
    RETURN salary * 0.1;
END;
$$ LANGUAGE plpgsql;`,
    description: "Create a stored function to encapsulate reusable business logic.",
    parameters: [
      { flag: "CREATE OR REPLACE FUNCTION", name: "create function", description: "Create or update a function" },
      { flag: "calculate_bonus", name: "function name", description: "Name of the function" },
      { flag: "(salary NUMERIC)", name: "parameters", description: "Input parameters with types" },
      { flag: "RETURNS NUMERIC", name: "return type", description: "Data type of the return value" },
      { flag: "$$", name: "dollar quoting", description: "Alternative to single quotes for function body" },
      { flag: "LANGUAGE plpgsql", name: "language", description: "Programming language (plpgsql, sql, python, etc.)" }
    ]
  },
  {
    path: ["Interacting with Data", "Stored Procedures and Functions", "Create Trigger"],
    command: `CREATE OR REPLACE FUNCTION update_modified_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.modified_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER tr_update_modified
    BEFORE UPDATE ON employees
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_time();`,
    description: "Create a trigger to automatically execute code when data changes.",
    parameters: [
      { flag: "CREATE TRIGGER", name: "create trigger", description: "Create a new trigger" },
      { flag: "tr_update_modified", name: "trigger name", description: "Name for the trigger" },
      { flag: "BEFORE UPDATE", name: "timing", description: "When to fire (BEFORE/AFTER INSERT/UPDATE/DELETE)" },
      { flag: "ON employees", name: "table", description: "Table the trigger is attached to" },
      { flag: "FOR EACH ROW", name: "granularity", description: "Fire for each affected row (vs FOR EACH STATEMENT)" },
      { flag: "EXECUTE FUNCTION", name: "execute function", description: "Function to call when trigger fires" }
    ]
  },
  {
    path: ["Interacting with Data", "Advanced Data Types", "Array Operations"],
    command: "SELECT name FROM employees WHERE skills @> ARRAY['PostgreSQL', 'Python'];",
    description: "Work with PostgreSQL array data types for storing multiple values in a single column.",
    parameters: [
      { flag: "ARRAY['item1', 'item2']", name: "array literal", description: "Create an array literal" },
      { flag: "@>", name: "contains operator", description: "Check if left array contains right array" },
      { flag: "&&", name: "overlap operator", description: "Check if arrays have common elements" },
      { flag: "array_length()", name: "array length", description: "Get the length of an array" },
      { flag: "unnest()", name: "unnest", description: "Expand array to rows" }
    ]
  },
  {
    path: ["Interacting with Data", "Advanced Data Types", "UUID Operations"],
    command: "CREATE TABLE sessions (id UUID DEFAULT gen_random_uuid(), user_id INT, created_at TIMESTAMP);",
    description: "Use UUID (Universally Unique Identifier) data type for globally unique identifiers.",
    parameters: [
      { flag: "UUID", name: "uuid type", description: "UUID data type for unique identifiers" },
      { flag: "gen_random_uuid()", name: "generate uuid", description: "Function to generate a random UUID" },
      { flag: "uuid_generate_v4()", name: "uuid v4", description: "Generate UUID version 4 (requires uuid-ossp extension)" },
      { flag: "::uuid", name: "cast to uuid", description: "Cast a string to UUID type" }
    ]
  }
];

// Validation function for this module
export function validateDataInteractionCommands() {
  return dataInteractionCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Interacting with Data"
  );
}