// Performance Monitoring and Maintenance Commands
export const performanceCommands = [
  {
    path: ["Performance Monitoring and Maintenance", "Monitoring Server Activity", "Show Active Connections"],
    command: "SELECT pid, usename, datname, client_addr, state, query FROM pg_stat_activity;",
    description: "View all current client connections, their status, and current queries. Useful for identifying stuck processes."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Monitoring Server Activity", "Terminate a Specific Connection"],
    command: "SELECT pg_terminate_backend(pid);",
    description: "Terminate a specific backend process. Replace 'pid' with the actual process ID from pg_stat_activity. Requires superuser privileges."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Analyzing Query Performance", "Show Execution Plan"],
    command: "EXPLAIN SELECT * FROM employees WHERE hire_date > '2023-01-01';",
    description: "Display the query execution plan without running the query. Shows if indexes are used and estimated costs."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Analyzing Query Performance", "Execute and Analyze Query"],
    command: "EXPLAIN ANALYZE SELECT * FROM employees WHERE last_name = '<PERSON>';",
    description: "Execute the query and show the actual execution plan with timing information. Invaluable for identifying performance bottlenecks."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Vacuum", "Standard Vacuum"],
    command: "VACUUM your_table_name;",
    description: "Reclaim storage space from deleted rows. Safe to run on live databases."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Vacuum", "Verbose Vacuum"],
    command: "VACUUM VERBOSE your_table_name;",
    description: "Run vacuum with detailed output showing what operations are performed."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Vacuum", "Full Vacuum"],
    command: "VACUUM FULL your_table_name;",
    description: "Rewrite the entire table to reclaim all excess space. Requires exclusive lock and can be slow on large tables."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Update Planner Statistics"],
    command: "ANALYZE your_table_name;",
    description: "Collect statistics about data distribution for the query planner. Run after large data changes."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Vacuum and Analyze"],
    command: "VACUUM ANALYZE your_table_name;",
    description: "Perform both vacuum and analyze operations in one command."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Reindex", "Reindex a Specific Index"],
    command: "REINDEX INDEX your_index_name;",
    description: "Rebuild a specific index to improve performance."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Reindex", "Reindex All Indexes on a Table"],
    command: "REINDEX TABLE your_table_name;",
    description: "Rebuild all indexes on a table. Locks the table during operation."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Checking Object Sizes", "Show Database Size"],
    command: "SELECT pg_database_size('your_database_name');",
    description: "Get the size of a specific database. Use pg_size_pretty() for human-readable format."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Checking Object Sizes", "Show Table Size"],
    command: "SELECT pg_size_pretty(pg_relation_size('your_table_name'));",
    description: "Get the size of a specific table, excluding indexes."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Checking Object Sizes", "Show Total Table Size"],
    command: "SELECT pg_size_pretty(pg_total_relation_size('your_table_name'));",
    description: "Get the total size of a table including all its indexes.",
    parameters: [
      { flag: "pg_total_relation_size()", name: "total size function", description: "Get total size including indexes and TOAST" },
      { flag: "pg_size_pretty()", name: "format function", description: "Format bytes into human-readable format" },
      { flag: "'your_table_name'", name: "table name", description: "Name of the table to check" }
    ]
  },
  {
    path: ["Performance Monitoring and Maintenance", "Advanced Monitoring", "Show Blocking Queries"],
    command: "SELECT blocked_locks.pid AS blocked_pid, blocked_activity.usename AS blocked_user, blocking_locks.pid AS blocking_pid, blocking_activity.usename AS blocking_user, blocked_activity.query AS blocked_statement, blocking_activity.query AS current_statement_in_blocking_process FROM pg_catalog.pg_locks blocked_locks JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype AND blocking_locks.DATABASE IS NOT DISTINCT FROM blocked_locks.DATABASE AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid AND blocking_locks.pid != blocked_locks.pid JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid WHERE NOT blocked_locks.GRANTED;",
    description: "Identify queries that are blocking other queries, useful for diagnosing deadlocks and performance issues.",
    parameters: [
      { flag: "blocked_pid", name: "blocked process", description: "Process ID of the blocked query" },
      { flag: "blocking_pid", name: "blocking process", description: "Process ID of the query causing the block" },
      { flag: "blocked_user", name: "blocked user", description: "User running the blocked query" },
      { flag: "blocking_user", name: "blocking user", description: "User running the blocking query" },
      { flag: "pg_locks", name: "locks table", description: "System view showing current locks" },
      { flag: "NOT GRANTED", name: "waiting locks", description: "Filter for locks that are waiting" }
    ]
  },
  {
    path: ["Performance Monitoring and Maintenance", "Advanced Monitoring", "Show Long Running Queries"],
    command: "SELECT pid, now() - pg_stat_activity.query_start AS duration, query FROM pg_stat_activity WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes' AND state = 'active';",
    description: "Find queries that have been running for more than 5 minutes.",
    parameters: [
      { flag: "pid", name: "process id", description: "Process ID of the long-running query" },
      { flag: "query_start", name: "start time", description: "When the query started" },
      { flag: "now() - query_start", name: "duration", description: "How long the query has been running" },
      { flag: "interval '5 minutes'", name: "threshold", description: "Minimum duration to consider 'long running'" },
      { flag: "state = 'active'", name: "active filter", description: "Only show currently executing queries" }
    ]
  },
  {
    path: ["Performance Monitoring and Maintenance", "Advanced Monitoring", "Show Table Statistics"],
    command: "SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del, n_live_tup, n_dead_tup, last_vacuum, last_autovacuum, last_analyze, last_autoanalyze FROM pg_stat_user_tables ORDER BY n_dead_tup DESC;",
    description: "Display comprehensive statistics about table usage and maintenance needs.",
    parameters: [
      { flag: "n_tup_ins", name: "inserts", description: "Number of rows inserted since last stats reset" },
      { flag: "n_tup_upd", name: "updates", description: "Number of rows updated since last stats reset" },
      { flag: "n_tup_del", name: "deletes", description: "Number of rows deleted since last stats reset" },
      { flag: "n_live_tup", name: "live tuples", description: "Estimated number of live rows" },
      { flag: "n_dead_tup", name: "dead tuples", description: "Estimated number of dead rows (need vacuuming)" },
      { flag: "last_vacuum", name: "last vacuum", description: "Last time table was manually vacuumed" },
      { flag: "last_autovacuum", name: "last autovacuum", description: "Last time table was auto-vacuumed" }
    ]
  },
  {
    path: ["Performance Monitoring and Maintenance", "Advanced Monitoring", "Show Index Usage Statistics"],
    command: "SELECT schemaname, tablename, indexname, idx_tup_read, idx_tup_fetch, idx_scan FROM pg_stat_user_indexes ORDER BY idx_scan DESC;",
    description: "Monitor index usage to identify unused or heavily used indexes.",
    parameters: [
      { flag: "idx_scan", name: "index scans", description: "Number of index scans initiated on this index" },
      { flag: "idx_tup_read", name: "tuples read", description: "Number of index entries returned by scans" },
      { flag: "idx_tup_fetch", name: "tuples fetched", description: "Number of live table rows fetched by index scans" },
      { flag: "ORDER BY idx_scan DESC", name: "sort by usage", description: "Sort by most frequently used indexes" }
    ]
  },
  {
    path: ["Performance Monitoring and Maintenance", "Advanced Monitoring", "Show Cache Hit Ratios"],
    command: "SELECT 'index hit rate' AS name, (sum(idx_blks_hit)) / nullif(sum(idx_blks_hit + idx_blks_read),0) AS ratio FROM pg_statio_user_indexes UNION ALL SELECT 'table hit rate' AS name, sum(heap_blks_hit) / nullif(sum(heap_blks_hit) + sum(heap_blks_read),0) AS ratio FROM pg_statio_user_tables;",
    description: "Calculate cache hit ratios for tables and indexes to assess memory efficiency.",
    parameters: [
      { flag: "idx_blks_hit", name: "index blocks hit", description: "Number of index blocks found in cache" },
      { flag: "idx_blks_read", name: "index blocks read", description: "Number of index blocks read from disk" },
      { flag: "heap_blks_hit", name: "table blocks hit", description: "Number of table blocks found in cache" },
      { flag: "heap_blks_read", name: "table blocks read", description: "Number of table blocks read from disk" },
      { flag: "nullif()", name: "null if zero", description: "Avoid division by zero errors" }
    ]
  },
  {
    path: ["Performance Monitoring and Maintenance", "Query Analysis", "Show Most Expensive Queries"],
    command: "SELECT query, calls, total_time, mean_time, rows FROM pg_stat_statements ORDER BY total_time DESC LIMIT 10;",
    description: "Display the most time-consuming queries (requires pg_stat_statements extension).",
    parameters: [
      { flag: "pg_stat_statements", name: "extension", description: "Extension that tracks query statistics" },
      { flag: "calls", name: "call count", description: "Number of times the query was executed" },
      { flag: "total_time", name: "total time", description: "Total time spent executing this query" },
      { flag: "mean_time", name: "average time", description: "Average execution time per call" },
      { flag: "rows", name: "rows returned", description: "Total number of rows returned by all executions" }
    ]
  },
  {
    path: ["Performance Monitoring and Maintenance", "System Resources", "Show Database Connections by State"],
    command: "SELECT state, count(*) FROM pg_stat_activity GROUP BY state;",
    description: "Monitor connection states to understand database load and connection pooling effectiveness.",
    parameters: [
      { flag: "state", name: "connection state", description: "Current state of the connection" },
      { flag: "active", name: "active state", description: "Currently executing a query" },
      { flag: "idle", name: "idle state", description: "Waiting for a new client command" },
      { flag: "idle in transaction", name: "idle in transaction", description: "In a transaction but not executing a query" },
      { flag: "count(*)", name: "connection count", description: "Number of connections in each state" }
    ]
  }
];

// Validation function for this module
export function validatePerformanceCommands() {
  return performanceCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Performance Monitoring and Maintenance"
  );
}