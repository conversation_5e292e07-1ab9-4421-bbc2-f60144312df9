// Performance Monitoring and Maintenance Commands
export const performanceCommands = [
  {
    path: ["Performance Monitoring and Maintenance", "Monitoring Server Activity", "Show Active Connections"],
    command: "SELECT pid, usename, datname, client_addr, state, query FROM pg_stat_activity;",
    description: "View all current client connections, their status, and current queries. Useful for identifying stuck processes."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Monitoring Server Activity", "Terminate a Specific Connection"],
    command: "SELECT pg_terminate_backend(pid);",
    description: "Terminate a specific backend process. Replace 'pid' with the actual process ID from pg_stat_activity. Requires superuser privileges."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Analyzing Query Performance", "Show Execution Plan"],
    command: "EXPLAIN SELECT * FROM employees WHERE hire_date > '2023-01-01';",
    description: "Display the query execution plan without running the query. Shows if indexes are used and estimated costs."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Analyzing Query Performance", "Execute and Analyze Query"],
    command: "EXPLAIN ANALYZE SELECT * FROM employees WHERE last_name = '<PERSON>';",
    description: "Execute the query and show the actual execution plan with timing information. Invaluable for identifying performance bottlenecks."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Vacuum", "Standard Vacuum"],
    command: "VACUUM your_table_name;",
    description: "Reclaim storage space from deleted rows. Safe to run on live databases."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Vacuum", "Verbose Vacuum"],
    command: "VACUUM VERBOSE your_table_name;",
    description: "Run vacuum with detailed output showing what operations are performed."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Vacuum", "Full Vacuum"],
    command: "VACUUM FULL your_table_name;",
    description: "Rewrite the entire table to reclaim all excess space. Requires exclusive lock and can be slow on large tables."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Update Planner Statistics"],
    command: "ANALYZE your_table_name;",
    description: "Collect statistics about data distribution for the query planner. Run after large data changes."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Vacuum and Analyze"],
    command: "VACUUM ANALYZE your_table_name;",
    description: "Perform both vacuum and analyze operations in one command."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Reindex", "Reindex a Specific Index"],
    command: "REINDEX INDEX your_index_name;",
    description: "Rebuild a specific index to improve performance."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Database Maintenance", "Reindex", "Reindex All Indexes on a Table"],
    command: "REINDEX TABLE your_table_name;",
    description: "Rebuild all indexes on a table. Locks the table during operation."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Checking Object Sizes", "Show Database Size"],
    command: "SELECT pg_database_size('your_database_name');",
    description: "Get the size of a specific database. Use pg_size_pretty() for human-readable format."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Checking Object Sizes", "Show Table Size"],
    command: "SELECT pg_size_pretty(pg_relation_size('your_table_name'));",
    description: "Get the size of a specific table, excluding indexes."
  },
  {
    path: ["Performance Monitoring and Maintenance", "Checking Object Sizes", "Show Total Table Size"],
    command: "SELECT pg_size_pretty(pg_total_relation_size('your_table_name'));",
    description: "Get the total size of a table including all its indexes."
  }
];

// Validation function for this module
export function validatePerformanceCommands() {
  return performanceCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Performance Monitoring and Maintenance"
  );
}