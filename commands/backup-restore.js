// Backup and Restore Commands
export const backupRestoreCommands = [
  {
    path: ["Backup and Restore", "Backing Up a Single Database", "Plain-Text SQL Dump"],
    command: "pg_dump -U your_user -d your_database > your_database_backup.sql",
    description: "This method creates a human-readable .sql file containing SQL commands to recreate the database. Ideal for small databases, version control, or when you need to edit the backup file before restoring. The file will contain CREATE TABLE statements, INSERT statements, and other SQL commands.",
    parameters: [
      { flag: "-U", name: "username", description: "Specifies the PostgreSQL user to connect with. Replace 'your_user' with your actual PostgreSQL username that has backup privileges." },
      { flag: "-d", name: "database", description: "The name of the database you want to back up. Replace 'your_database' with the actual database name." },
      { flag: ">", name: "output file", description: "Shell redirection operator that saves the output to a file. Replace 'your_database_backup.sql' with your desired backup filename." }
    ]
  },
  {
    path: ["Backup and Restore", "Backing Up a Single Database", "Custom Format (Compressed) Dump"],
    command: "pg_dump -U your_user -d your_database -F c -f your_database_backup.dump",
    description: "Creates a compressed binary file (.dump) that is smaller than plain-text and more flexible for restoring. This format allows you to selectively restore parts of the database (like specific tables) and is generally more resilient to errors during restoration. Recommended for most production backups.",
    parameters: [
      { flag: "-U", name: "username", description: "Specifies the PostgreSQL user to connect with. Replace 'your_user' with your actual PostgreSQL username that has backup privileges." },
      { flag: "-d", name: "database", description: "The name of the database you want to back up. Replace 'your_database' with the actual database name." },
      { flag: "-F c", name: "format", description: "Specifies the output format. 'c' stands for custom format, which is compressed and flexible." },
      { flag: "-f", name: "output file", description: "Specifies the output file name. Replace 'your_database_backup.dump' with your desired backup filename (typically ends with .dump)." }
    ]
  },
  {
    path: ["Backup and Restore", "Backing Up All Databases"],
    command: "pg_dumpall -U postgres > full_server_backup.sql",
    description: "Create a backup of the entire PostgreSQL cluster including all databases, users, and tablespace definitions."
  },
  {
    path: ["Backup and Restore", "Restoring a Database", "From a Plain-Text SQL Dump", "Create a New Database"],
    command: "createdb -U your_user new_database_name",
    description: "Create an empty database to import the backup into. This command-line utility creates a new PostgreSQL database.",
    parameters: [
      { flag: "-U", name: "username", description: "Specifies the PostgreSQL user to connect with. Replace 'your_user' with your actual PostgreSQL username." },
      { flag: "new_database_name", name: "database name", description: "The name of the new database to create. Replace 'new_database_name' with your desired database name." }
    ]
  },
  {
    path: ["Backup and Restore", "Restoring a Database", "From a Plain-Text SQL Dump", "Import the Backup"],
    command: "psql -U your_user -d new_database_name < your_database_backup.sql",
    description: "Restore the database from the SQL dump file."
  },
  {
    path: ["Backup and Restore", "Restoring a Database", "From a Custom Format Dump", "Create a New Database"],
    command: "createdb -U your_user new_database_name",
    description: "Create an empty database for the restoration."
  },
  {
    path: ["Backup and Restore", "Restoring a Database", "From a Custom Format Dump", "Restore with pg_restore"],
    command: "pg_restore -U your_user -d new_database_name your_database_backup.dump",
    description: "Use pg_restore to load data from a custom format dump. Offers options for parallel processing and selective restoration."
  },
  {
    path: ["Backup and Restore", "Restoring a Database", "From a pg_dumpall Backup"],
    command: "psql -U postgres -f full_server_backup.sql",
    description: "Restore an entire cluster backup. Do not create databases beforehand as the backup includes creation commands."
  }
];

// Validation function for this module
export function validateBackupRestoreCommands() {
  return backupRestoreCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Backup and Restore"
  );
}