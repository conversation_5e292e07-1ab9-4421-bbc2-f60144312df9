// Backup and Restore Commands
export const backupRestoreCommands = [
  {
    path: ["Backup and Restore", "Backing Up a Single Database", "Plain-Text SQL Dump"],
    command: "pg_dump -U your_user -d your_database > your_database_backup.sql",
    description: "This method creates a human-readable .sql file containing SQL commands to recreate the database. Ideal for small databases, version control, or when you need to edit the backup file before restoring. The file will contain CREATE TABLE statements, INSERT statements, and other SQL commands.",
    parameters: [
      { flag: "-U", name: "username", description: "Specifies the PostgreSQL user to connect with. Replace 'your_user' with your actual PostgreSQL username that has backup privileges." },
      { flag: "-d", name: "database", description: "The name of the database you want to back up. Replace 'your_database' with the actual database name." },
      { flag: ">", name: "output file", description: "Shell redirection operator that saves the output to a file. Replace 'your_database_backup.sql' with your desired backup filename." }
    ]
  },
  {
    path: ["Backup and Restore", "Backing Up a Single Database", "Custom Format (Compressed) Dump"],
    command: "pg_dump -U your_user -d your_database -F c -f your_database_backup.dump",
    description: "Creates a compressed binary file (.dump) that is smaller than plain-text and more flexible for restoring. This format allows you to selectively restore parts of the database (like specific tables) and is generally more resilient to errors during restoration. Recommended for most production backups.",
    parameters: [
      { flag: "-U", name: "username", description: "Specifies the PostgreSQL user to connect with. Replace 'your_user' with your actual PostgreSQL username that has backup privileges." },
      { flag: "-d", name: "database", description: "The name of the database you want to back up. Replace 'your_database' with the actual database name." },
      { flag: "-F c", name: "format", description: "Specifies the output format. 'c' stands for custom format, which is compressed and flexible." },
      { flag: "-f", name: "output file", description: "Specifies the output file name. Replace 'your_database_backup.dump' with your desired backup filename (typically ends with .dump)." }
    ]
  },
  {
    path: ["Backup and Restore", "Backing Up All Databases"],
    command: "pg_dumpall -U postgres > full_server_backup.sql",
    description: "Create a backup of the entire PostgreSQL cluster including all databases, users, and tablespace definitions.",
    parameters: [
      { flag: "-U", name: "username", description: "Specifies the PostgreSQL user to connect with. Usually 'postgres' for cluster-wide operations." },
      { flag: "-h", name: "host", description: "Database server host or socket directory (default: local socket)" },
      { flag: "-p", name: "port", description: "Database server port number (default: 5432)" },
      { flag: "-W", name: "password", description: "Force password prompt (should happen automatically)" },
      { flag: "--clean", name: "clean", description: "Include commands to clean (drop) databases before recreating them" },
      { flag: "--if-exists", name: "if-exists", description: "Use IF EXISTS when dropping objects (requires --clean)" },
      { flag: ">", name: "output file", description: "Shell redirection operator that saves the output to a file" }
    ]
  },
  {
    path: ["Backup and Restore", "Restoring a Database", "From a Plain-Text SQL Dump", "Create a New Database"],
    command: "createdb -U your_user new_database_name",
    description: "Create an empty database to import the backup into. This command-line utility creates a new PostgreSQL database.",
    parameters: [
      { flag: "-U", name: "username", description: "Specifies the PostgreSQL user to connect with. Replace 'your_user' with your actual PostgreSQL username." },
      { flag: "new_database_name", name: "database name", description: "The name of the new database to create. Replace 'new_database_name' with your desired database name." }
    ]
  },
  {
    path: ["Backup and Restore", "Restoring a Database", "From a Plain-Text SQL Dump", "Import the Backup"],
    command: "psql -U your_user -d new_database_name < your_database_backup.sql",
    description: "Restore the database from the SQL dump file.",
    parameters: [
      { flag: "-U", name: "username", description: "Specifies the PostgreSQL user to connect with" },
      { flag: "-d", name: "database", description: "The target database name to restore into" },
      { flag: "-h", name: "host", description: "Database server host (default: local socket)" },
      { flag: "-p", name: "port", description: "Database server port (default: 5432)" },
      { flag: "-W", name: "password", description: "Force password prompt" },
      { flag: "<", name: "input file", description: "Shell redirection operator that reads from the backup file" }
    ]
  },
  {
    path: ["Backup and Restore", "Restoring a Database", "From a Custom Format Dump", "Create a New Database"],
    command: "createdb -U your_user new_database_name",
    description: "Create an empty database for the restoration.",
    parameters: [
      { flag: "-U", name: "username", description: "Specifies the PostgreSQL user to connect with" },
      { flag: "-h", name: "host", description: "Database server host (default: local socket)" },
      { flag: "-p", name: "port", description: "Database server port (default: 5432)" },
      { flag: "-W", name: "password", description: "Force password prompt" },
      { flag: "-O", name: "owner", description: "Database owner (default: user executing the command)" },
      { flag: "-T", name: "template", description: "Template database to copy (default: template1)" },
      { flag: "new_database_name", name: "database name", description: "The name of the new database to create" }
    ]
  },
  {
    path: ["Backup and Restore", "Restoring a Database", "From a Custom Format Dump", "Restore with pg_restore"],
    command: "pg_restore -U your_user -d new_database_name your_database_backup.dump",
    description: "Use pg_restore to load data from a custom format dump. Offers options for parallel processing and selective restoration.",
    parameters: [
      { flag: "-U", name: "username", description: "Specifies the PostgreSQL user to connect with" },
      { flag: "-d", name: "database", description: "The target database name to restore into" },
      { flag: "-h", name: "host", description: "Database server host (default: local socket)" },
      { flag: "-p", name: "port", description: "Database server port (default: 5432)" },
      { flag: "-W", name: "password", description: "Force password prompt" },
      { flag: "-j", name: "jobs", description: "Number of parallel jobs for faster restoration (e.g., -j 4)" },
      { flag: "-v", name: "verbose", description: "Verbose mode - show detailed progress information" },
      { flag: "-c", name: "clean", description: "Clean (drop) database objects before recreating them" },
      { flag: "-C", name: "create", description: "Create the database before restoring into it" },
      { flag: "-t", name: "table", description: "Restore only the named table(s)" },
      { flag: "-n", name: "schema", description: "Restore only objects in the named schema(s)" }
    ]
  },
  {
    path: ["Backup and Restore", "Restoring a Database", "From a pg_dumpall Backup"],
    command: "psql -U postgres -f full_server_backup.sql",
    description: "Restore an entire cluster backup. Do not create databases beforehand as the backup includes creation commands.",
    parameters: [
      { flag: "-U", name: "username", description: "Specifies the PostgreSQL user to connect with (usually postgres for cluster operations)" },
      { flag: "-f", name: "file", description: "Execute commands from the specified file" },
      { flag: "-h", name: "host", description: "Database server host (default: local socket)" },
      { flag: "-p", name: "port", description: "Database server port (default: 5432)" },
      { flag: "-W", name: "password", description: "Force password prompt" },
      { flag: "-v", name: "variable", description: "Set psql variable (e.g., -v ON_ERROR_STOP=1)" }
    ]
  },
  {
    path: ["Backup and Restore", "Advanced Backup Options", "Parallel Backup (Directory Format)"],
    command: "pg_dump -U your_user -d your_database -j 4 -F d -f backup_directory",
    description: "Create a parallel backup using multiple jobs for faster processing. Uses directory format which allows parallel operations.",
    parameters: [
      { flag: "-U", name: "username", description: "PostgreSQL user with backup privileges" },
      { flag: "-d", name: "database", description: "Database to back up" },
      { flag: "-j", name: "jobs", description: "Number of parallel jobs (4 in this example, adjust based on CPU cores)" },
      { flag: "-F d", name: "format", description: "Directory format - creates a directory with multiple files" },
      { flag: "-f", name: "output", description: "Output directory path (will be created if it doesn't exist)" },
      { flag: "-h", name: "host", description: "Database server host (default: local socket)" },
      { flag: "-p", name: "port", description: "Database server port (default: 5432)" }
    ]
  },
  {
    path: ["Backup and Restore", "Advanced Backup Options", "Schema-Only Backup"],
    command: "pg_dump -U your_user -d your_database -s -f schema_only_backup.sql",
    description: "Create a backup containing only the database schema (tables, indexes, functions) without data.",
    parameters: [
      { flag: "-U", name: "username", description: "PostgreSQL user with backup privileges" },
      { flag: "-d", name: "database", description: "Database to back up" },
      { flag: "-s", name: "schema-only", description: "Dump only the object definitions (schema), not data" },
      { flag: "-f", name: "output file", description: "Output file name for the schema backup" },
      { flag: "-h", name: "host", description: "Database server host (default: local socket)" },
      { flag: "-p", name: "port", description: "Database server port (default: 5432)" }
    ]
  },
  {
    path: ["Backup and Restore", "Advanced Backup Options", "Data-Only Backup"],
    command: "pg_dump -U your_user -d your_database -a -f data_only_backup.sql",
    description: "Create a backup containing only the data (INSERT statements) without schema definitions.",
    parameters: [
      { flag: "-U", name: "username", description: "PostgreSQL user with backup privileges" },
      { flag: "-d", name: "database", description: "Database to back up" },
      { flag: "-a", name: "data-only", description: "Dump only the data, not the schema" },
      { flag: "-f", name: "output file", description: "Output file name for the data backup" },
      { flag: "--column-inserts", name: "column-inserts", description: "Use column names in INSERT commands (more readable but larger)" },
      { flag: "--inserts", name: "inserts", description: "Use INSERT commands instead of COPY (slower but more compatible)" }
    ]
  },
  {
    path: ["Backup and Restore", "Advanced Backup Options", "Selective Table Backup"],
    command: "pg_dump -U your_user -d your_database -t table_name -f table_backup.sql",
    description: "Back up only specific tables from a database.",
    parameters: [
      { flag: "-U", name: "username", description: "PostgreSQL user with backup privileges" },
      { flag: "-d", name: "database", description: "Database containing the table" },
      { flag: "-t", name: "table", description: "Table name to back up (can be used multiple times for multiple tables)" },
      { flag: "-f", name: "output file", description: "Output file name for the table backup" },
      { flag: "-n", name: "schema", description: "Include only tables in the specified schema" },
      { flag: "--exclude-table", name: "exclude-table", description: "Exclude specific tables from backup" }
    ]
  },
  {
    path: ["Backup and Restore", "Advanced Restore Options", "Parallel Restore from Directory"],
    command: "pg_restore -U your_user -d target_database -j 4 backup_directory",
    description: "Restore from a directory format backup using parallel jobs for faster processing.",
    parameters: [
      { flag: "-U", name: "username", description: "PostgreSQL user with restore privileges" },
      { flag: "-d", name: "database", description: "Target database to restore into" },
      { flag: "-j", name: "jobs", description: "Number of parallel jobs (should match or be less than backup jobs)" },
      { flag: "-v", name: "verbose", description: "Verbose mode for detailed progress information" },
      { flag: "-c", name: "clean", description: "Clean (drop) database objects before recreating" },
      { flag: "--if-exists", name: "if-exists", description: "Use IF EXISTS when dropping objects (requires -c)" }
    ]
  },
  {
    path: ["Backup and Restore", "Advanced Restore Options", "Selective Schema Restore"],
    command: "pg_restore -U your_user -d target_database -n schema_name backup_file.dump",
    description: "Restore only objects from a specific schema.",
    parameters: [
      { flag: "-U", name: "username", description: "PostgreSQL user with restore privileges" },
      { flag: "-d", name: "database", description: "Target database to restore into" },
      { flag: "-n", name: "schema", description: "Restore only objects in the named schema" },
      { flag: "-t", name: "table", description: "Restore only the named table(s)" },
      { flag: "-I", name: "index", description: "Restore only the named index" },
      { flag: "-T", name: "trigger", description: "Restore only the named trigger" }
    ]
  },
  {
    path: ["Backup and Restore", "Advanced Restore Options", "List Backup Contents"],
    command: "pg_restore -l backup_file.dump",
    description: "List the contents of a backup file without restoring. Useful for selective restoration planning.",
    parameters: [
      { flag: "-l", name: "list", description: "List the contents of the backup file" },
      { flag: "-v", name: "verbose", description: "Verbose mode for more detailed information" },
      { flag: "-f", name: "file", description: "Save the table of contents to a file for editing" }
    ]
  },
  {
    path: ["Backup and Restore", "Advanced Restore Options", "Restore with Custom TOC"],
    command: "pg_restore -U your_user -d target_database -L toc_file.txt backup_file.dump",
    description: "Restore using a custom table of contents file for selective restoration.",
    parameters: [
      { flag: "-U", name: "username", description: "PostgreSQL user with restore privileges" },
      { flag: "-d", name: "database", description: "Target database to restore into" },
      { flag: "-L", name: "list-file", description: "Use table of contents from this file (created with -l option)" },
      { flag: "-v", name: "verbose", description: "Verbose mode for detailed progress information" },
      { flag: "--single-transaction", name: "single-transaction", description: "Restore as a single transaction (all or nothing)" }
    ]
  },
  {
    path: ["Backup and Restore", "Backup Verification", "Test Backup Integrity"],
    command: "pg_restore -l backup_file.dump > /dev/null",
    description: "Test if a backup file is readable and not corrupted without actually restoring data.",
    parameters: [
      { flag: "-l", name: "list", description: "List contents (tests file integrity)" },
      { flag: "> /dev/null", name: "redirect", description: "Discard output, only check for errors" },
      { flag: "-v", name: "verbose", description: "Show verbose output to see any warnings" }
    ]
  },
  {
    path: ["Backup and Restore", "Backup Verification", "Compare Database Schemas"],
    command: "pg_dump -s -d database1 | diff - <(pg_dump -s -d database2)",
    description: "Compare schemas between two databases to verify backup/restore accuracy.",
    parameters: [
      { flag: "-s", name: "schema-only", description: "Dump only schema for comparison" },
      { flag: "-d", name: "database", description: "Database name to compare" },
      { flag: "diff", name: "diff command", description: "Unix diff command to compare outputs" },
      { flag: "<()", name: "process substitution", description: "Bash process substitution for second database" }
    ]
  }
];

// Validation function for this module
export function validateBackupRestoreCommands() {
  return backupRestoreCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Backup and Restore"
  );
}