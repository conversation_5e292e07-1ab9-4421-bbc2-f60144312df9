// Replication and High Availability Commands
export const replicationCommands = [
  {
    path: ["Replication and High Availability", "Streaming Replication", "Setup Primary Server"],
    command: "ALTER SYSTEM SET wal_level = replica;",
    description: "Configure the primary server for streaming replication by setting appropriate WAL level.",
    parameters: [
      { flag: "wal_level", name: "wal level", description: "Write-Ahead Log level (minimal, replica, logical)" },
      { flag: "max_wal_senders", name: "max wal senders", description: "Maximum number of concurrent WAL sender processes" },
      { flag: "wal_keep_segments", name: "wal keep segments", description: "Minimum number of WAL files to keep for standby servers" },
      { flag: "archive_mode", name: "archive mode", description: "Enable WAL archiving (on, off, always)" },
      { flag: "archive_command", name: "archive command", description: "Command to archive completed WAL files" }
    ]
  },
  {
    path: ["Replication and High Availability", "Streaming Replication", "Create Replication User"],
    command: "CREATE USER replicator REPLICATION LOGIN PASSWORD 'secure_password';",
    description: "Create a dedicated user with replication privileges for standby servers to connect.",
    parameters: [
      { flag: "CREATE USER", name: "create user", description: "Create a new database user" },
      { flag: "REPLICATION", name: "replication privilege", description: "Grant replication privileges to the user" },
      { flag: "LOGIN", name: "login privilege", description: "Allow the user to log in" },
      { flag: "PASSWORD", name: "password", description: "Set password for the replication user" },
      { flag: "CONNECTION LIMIT", name: "connection limit", description: "Limit concurrent connections for this user" }
    ]
  },
  {
    path: ["Replication and High Availability", "Streaming Replication", "Setup Standby Server"],
    command: "pg_basebackup -h primary_host -D /var/lib/postgresql/data -U replicator -W",
    description: "Create a base backup from the primary server to initialize a standby server.",
    parameters: [
      { flag: "-h", name: "host", description: "Hostname or IP of the primary server" },
      { flag: "-D", name: "directory", description: "Target directory for the backup" },
      { flag: "-U", name: "username", description: "Replication user to connect as" },
      { flag: "-W", name: "password", description: "Prompt for password" },
      { flag: "-P", name: "progress", description: "Show progress information" },
      { flag: "-R", name: "write recovery", description: "Write recovery.conf for standby mode" },
      { flag: "-X stream", name: "wal method", description: "Stream WAL files during backup" }
    ]
  },
  {
    path: ["Replication and High Availability", "Streaming Replication", "Check Replication Status"],
    command: "SELECT client_addr, state, sync_state FROM pg_stat_replication;",
    description: "Monitor replication status and connected standby servers from the primary.",
    parameters: [
      { flag: "client_addr", name: "client address", description: "IP address of the standby server" },
      { flag: "state", name: "replication state", description: "Current state (startup, catchup, streaming)" },
      { flag: "sync_state", name: "sync state", description: "Synchronization state (async, sync, potential)" },
      { flag: "sent_lsn", name: "sent lsn", description: "Last WAL location sent to standby" },
      { flag: "flush_lsn", name: "flush lsn", description: "Last WAL location flushed by standby" }
    ]
  },
  {
    path: ["Replication and High Availability", "Logical Replication", "Create Publication"],
    command: "CREATE PUBLICATION my_publication FOR ALL TABLES;",
    description: "Create a publication to define which tables should be replicated using logical replication.",
    parameters: [
      { flag: "CREATE PUBLICATION", name: "create publication", description: "Create a new publication" },
      { flag: "my_publication", name: "publication name", description: "Name for the publication" },
      { flag: "FOR ALL TABLES", name: "all tables", description: "Include all tables in the publication" },
      { flag: "FOR TABLE", name: "specific tables", description: "Include only specified tables" },
      { flag: "WITH (publish = 'insert,update,delete')", name: "publish options", description: "Specify which operations to replicate" }
    ]
  },
  {
    path: ["Replication and High Availability", "Logical Replication", "Create Subscription"],
    command: "CREATE SUBSCRIPTION my_subscription CONNECTION 'host=primary_host dbname=mydb user=replicator' PUBLICATION my_publication;",
    description: "Create a subscription on the subscriber database to receive logical replication data.",
    parameters: [
      { flag: "CREATE SUBSCRIPTION", name: "create subscription", description: "Create a new subscription" },
      { flag: "my_subscription", name: "subscription name", description: "Name for the subscription" },
      { flag: "CONNECTION", name: "connection string", description: "Connection parameters to the publisher" },
      { flag: "PUBLICATION", name: "publication name", description: "Name of the publication to subscribe to" },
      { flag: "WITH (copy_data = false)", name: "copy data", description: "Whether to copy existing data initially" }
    ]
  },
  {
    path: ["Replication and High Availability", "Logical Replication", "Monitor Logical Replication"],
    command: "SELECT subname, pid, received_lsn, latest_end_lsn FROM pg_stat_subscription;",
    description: "Monitor logical replication subscription status and lag.",
    parameters: [
      { flag: "subname", name: "subscription name", description: "Name of the subscription" },
      { flag: "pid", name: "process id", description: "Process ID of the subscription worker" },
      { flag: "received_lsn", name: "received lsn", description: "Last WAL location received" },
      { flag: "latest_end_lsn", name: "latest end lsn", description: "Latest WAL location reported by publisher" },
      { flag: "last_msg_send_time", name: "last message time", description: "Time of last message from publisher" }
    ]
  },
  {
    path: ["Replication and High Availability", "Failover and Recovery", "Promote Standby Server"],
    command: "SELECT pg_promote();",
    description: "Promote a standby server to become the new primary server during failover.",
    parameters: [
      { flag: "pg_promote()", name: "promote function", description: "Function to promote standby to primary" },
      { flag: "pg_ctl promote", name: "pg_ctl promote", description: "Command-line alternative to promote standby" },
      { flag: "-D data_directory", name: "data directory", description: "Specify the data directory path" }
    ]
  },
  {
    path: ["Replication and High Availability", "Failover and Recovery", "Check Recovery Status"],
    command: "SELECT pg_is_in_recovery();",
    description: "Check if the server is currently in recovery mode (standby) or normal operation (primary).",
    parameters: [
      { flag: "pg_is_in_recovery()", name: "recovery check", description: "Returns true if server is in recovery mode" },
      { flag: "pg_last_wal_receive_lsn()", name: "last received wal", description: "Last WAL location received (standby only)" },
      { flag: "pg_last_wal_replay_lsn()", name: "last replayed wal", description: "Last WAL location replayed (standby only)" }
    ]
  },
  {
    path: ["Replication and High Availability", "Connection Pooling", "Configure pgBouncer"],
    command: "# Edit /etc/pgbouncer/pgbouncer.ini",
    description: "Configure pgBouncer connection pooler to manage database connections efficiently.",
    parameters: [
      { flag: "listen_addr", name: "listen address", description: "IP address for pgBouncer to listen on" },
      { flag: "listen_port", name: "listen port", description: "Port for pgBouncer to listen on (default: 6432)" },
      { flag: "pool_mode", name: "pool mode", description: "Pooling mode (session, transaction, statement)" },
      { flag: "max_client_conn", name: "max client connections", description: "Maximum number of client connections" },
      { flag: "default_pool_size", name: "default pool size", description: "Default number of server connections per database" }
    ]
  },
  {
    path: ["Replication and High Availability", "Load Balancing", "Setup HAProxy for PostgreSQL"],
    command: "# Configure HAProxy backend for PostgreSQL primary/standby",
    description: "Configure HAProxy to distribute read queries to standby servers and writes to primary.",
    parameters: [
      { flag: "backend postgresql_primary", name: "primary backend", description: "Backend configuration for write operations" },
      { flag: "backend postgresql_standby", name: "standby backend", description: "Backend configuration for read operations" },
      { flag: "option httpchk", name: "health check", description: "HTTP health check configuration" },
      { flag: "server primary", name: "primary server", description: "Primary server configuration" },
      { flag: "server standby", name: "standby server", description: "Standby server configuration with 'backup' flag" }
    ]
  }
];

// Validation function for this module
export function validateReplicationCommands() {
  return replicationCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Replication and High Availability"
  );
}
