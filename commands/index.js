// Command Registry and Loader
// This file imports all command modules and provides a centralized interface

import { installationCommands, validateInstallationCommands } from './installation.js';
import { databaseCommands, validateDatabaseCommands } from './databases.js';
import { dataInteractionCommands, validateDataInteractionCommands } from './data-interaction.js';
import { backupRestoreCommands, validateBackupRestoreCommands } from './backup-restore.js';
import { performanceCommands, validatePerformanceCommands } from './performance.js';
import { securityCommands, validateSecurityCommands } from './security.js';
import { replicationCommands, validateReplicationCommands } from './replication.js';
import { extensionsCommands, validateExtensionsCommands } from './extensions.js';
import { configurationCommands, validateConfigurationCommands } from './configuration.js';

// Registry of all command modules for easy management
export const commandModules = {
  installation: {
    name: 'Installation and Server Management',
    commands: installationCommands,
    validate: validateInstallationCommands
  },
  databases: {
    name: 'Managing Databases and Roles',
    commands: databaseCommands,
    validate: validateDatabaseCommands
  },
  'data-interaction': {
    name: 'Interacting with Data',
    commands: dataInteractionCommands,
    validate: validateDataInteractionCommands
  },
  'backup-restore': {
    name: 'Backup and Restore',
    commands: backupRestoreCommands,
    validate: validateBackupRestoreCommands
  },
  performance: {
    name: 'Performance Monitoring and Maintenance',
    commands: performanceCommands,
    validate: validatePerformanceCommands
  },
  security: {
    name: 'Security and Authentication',
    commands: securityCommands,
    validate: validateSecurityCommands
  },
  replication: {
    name: 'Replication and High Availability',
    commands: replicationCommands,
    validate: validateReplicationCommands
  },
  extensions: {
    name: 'Extensions and Advanced Features',
    commands: extensionsCommands,
    validate: validateExtensionsCommands
  },
  configuration: {
    name: 'Configuration Management',
    commands: configurationCommands,
    validate: validateConfigurationCommands
  }
};

// Combined array of all commands
export const allCommands = [
  ...installationCommands,
  ...databaseCommands,
  ...dataInteractionCommands,
  ...backupRestoreCommands,
  ...performanceCommands,
  ...securityCommands,
  ...replicationCommands,
  ...extensionsCommands,
  ...configurationCommands
];

// Validation function to check all modules
export function validateAllCommands() {
  const results = {};
  let allValid = true;

  for (const [key, module] of Object.entries(commandModules)) {
    const isValid = module.validate();
    results[key] = {
      name: module.name,
      valid: isValid,
      commandCount: module.commands.length
    };
    if (!isValid) {
      allValid = false;
    }
  }

  return {
    allValid,
    moduleResults: results,
    totalCommands: allCommands.length
  };
}

// Utility function to get commands by category
export function getCommandsByCategory(categoryName) {
  const module = Object.values(commandModules).find(mod => mod.name === categoryName);
  return module ? module.commands : [];
}

// Utility function to add a new command module
export function addCommandModule(key, moduleDefinition) {
  if (commandModules[key]) {
    throw new Error(`Command module '${key}' already exists`);
  }

  // Validate the module structure
  if (!moduleDefinition.name || !Array.isArray(moduleDefinition.commands) || typeof moduleDefinition.validate !== 'function') {
    throw new Error('Invalid module definition structure');
  }

  commandModules[key] = moduleDefinition;

  // Rebuild allCommands array
  const newAllCommands = [];
  for (const mod of Object.values(commandModules)) {
    newAllCommands.push(...mod.commands);
  }

  // Update the allCommands export (this is a bit of a hack since exports are read-only)
  // In a real application, you'd return the new array instead
  console.warn('allCommands array updated. In production, consider returning from a function instead.');

  return newAllCommands;
}

// Metadata about the command system
export const commandSystemMetadata = {
  version: '1.0.0',
  lastUpdated: new Date().toISOString(),
  categories: Object.keys(commandModules).length,
  totalCommands: allCommands.length,
  description: 'Modular PostgreSQL command reference system'
};

// Run validation on load and log results
const validationResult = validateAllCommands();
if (validationResult.allValid) {
  console.log(`✅ All ${validationResult.totalCommands} PostgreSQL commands loaded successfully`);
} else {
  console.error('❌ Some command modules failed validation:', validationResult.moduleResults);
}