// Configuration Management Commands
export const configurationCommands = [
  {
    path: ["Configuration Management", "Server Configuration", "View Current Settings"],
    command: "SHOW ALL;",
    description: "Display all current PostgreSQL configuration parameters and their values.",
    parameters: [
      { flag: "SHOW ALL", name: "show all", description: "Display all configuration parameters" },
      { flag: "SHOW parameter_name", name: "show specific", description: "Display a specific parameter value" },
      { flag: "SELECT * FROM pg_settings", name: "pg_settings", description: "Query configuration through system view" }
    ]
  },
  {
    path: ["Configuration Management", "Server Configuration", "Modify Configuration"],
    command: "ALTER SYSTEM SET shared_buffers = '256MB';",
    description: "Modify server configuration parameters. Changes require server restart for some parameters.",
    parameters: [
      { flag: "ALTER SYSTEM SET", name: "alter system", description: "Modify configuration in postgresql.auto.conf" },
      { flag: "shared_buffers", name: "parameter name", description: "Configuration parameter to modify" },
      { flag: "= '256MB'", name: "parameter value", description: "New value for the parameter" },
      { flag: "SELECT pg_reload_conf()", name: "reload config", description: "Reload configuration without restart (for some parameters)" }
    ]
  },
  {
    path: ["Configuration Management", "Server Configuration", "Reset Configuration"],
    command: "ALTER SYSTEM RESET shared_buffers;",
    description: "Reset a configuration parameter to its default value.",
    parameters: [
      { flag: "ALTER SYSTEM RESET", name: "reset parameter", description: "Reset parameter to default value" },
      { flag: "shared_buffers", name: "parameter name", description: "Parameter to reset" },
      { flag: "ALTER SYSTEM RESET ALL", name: "reset all", description: "Reset all parameters to defaults" }
    ]
  },
  {
    path: ["Configuration Management", "Memory Settings", "Configure Shared Buffers"],
    command: "ALTER SYSTEM SET shared_buffers = '25% of RAM';",
    description: "Configure shared_buffers, the amount of memory PostgreSQL uses for caching data.",
    parameters: [
      { flag: "shared_buffers", name: "shared buffers", description: "Memory for caching database pages" },
      { flag: "25% of RAM", name: "recommended size", description: "Typical recommendation is 25% of total RAM" },
      { flag: "effective_cache_size", name: "effective cache size", description: "Estimate of OS and PostgreSQL cache size" },
      { flag: "work_mem", name: "work memory", description: "Memory for sort and hash operations per connection" }
    ]
  },
  {
    path: ["Configuration Management", "Memory Settings", "Configure Work Memory"],
    command: "ALTER SYSTEM SET work_mem = '4MB';",
    description: "Set work_mem, the amount of memory for sorting and hash operations per connection.",
    parameters: [
      { flag: "work_mem", name: "work memory", description: "Memory per operation (sort, hash, etc.)" },
      { flag: "4MB", name: "typical value", description: "Common starting value, adjust based on workload" },
      { flag: "maintenance_work_mem", name: "maintenance work mem", description: "Memory for maintenance operations (VACUUM, CREATE INDEX)" },
      { flag: "max_connections", name: "max connections", description: "Consider this when setting work_mem" }
    ]
  },
  {
    path: ["Configuration Management", "Connection Settings", "Configure Max Connections"],
    command: "ALTER SYSTEM SET max_connections = 200;",
    description: "Set the maximum number of concurrent database connections.",
    parameters: [
      { flag: "max_connections", name: "max connections", description: "Maximum concurrent connections" },
      { flag: "200", name: "typical value", description: "Adjust based on application needs and resources" },
      { flag: "superuser_reserved_connections", name: "superuser reserved", description: "Connections reserved for superusers" },
      { flag: "connection_limit", name: "per-user limit", description: "Set per-user connection limits" }
    ]
  },
  {
    path: ["Configuration Management", "Logging Configuration", "Configure Log Level"],
    command: "ALTER SYSTEM SET log_min_messages = 'warning';",
    description: "Set the minimum message level that gets logged to the server log.",
    parameters: [
      { flag: "log_min_messages", name: "log level", description: "Minimum message level to log" },
      { flag: "warning", name: "warning level", description: "Log warnings and more severe messages" },
      { flag: "debug1-debug5", name: "debug levels", description: "Various debug levels for troubleshooting" },
      { flag: "info", name: "info level", description: "Log informational messages" },
      { flag: "notice", name: "notice level", description: "Log notices and more severe messages" }
    ]
  },
  {
    path: ["Configuration Management", "Logging Configuration", "Configure Slow Query Logging"],
    command: "ALTER SYSTEM SET log_min_duration_statement = 1000;",
    description: "Log SQL statements that take longer than the specified time (in milliseconds).",
    parameters: [
      { flag: "log_min_duration_statement", name: "slow query threshold", description: "Log queries taking longer than this (ms)" },
      { flag: "1000", name: "1 second", description: "Log queries taking more than 1 second" },
      { flag: "log_statement", name: "log statement", description: "What types of statements to log (none, ddl, mod, all)" },
      { flag: "log_duration", name: "log duration", description: "Log the duration of completed statements" }
    ]
  },
  {
    path: ["Configuration Management", "Performance Tuning", "Configure Checkpoint Settings"],
    command: "ALTER SYSTEM SET checkpoint_completion_target = 0.9;",
    description: "Configure checkpoint behavior to spread I/O load over time.",
    parameters: [
      { flag: "checkpoint_completion_target", name: "completion target", description: "Fraction of checkpoint interval to complete checkpoint" },
      { flag: "0.9", name: "recommended value", description: "Complete checkpoint in 90% of interval" },
      { flag: "checkpoint_timeout", name: "checkpoint timeout", description: "Maximum time between checkpoints" },
      { flag: "max_wal_size", name: "max wal size", description: "Maximum WAL size before forcing checkpoint" }
    ]
  },
  {
    path: ["Configuration Management", "Performance Tuning", "Configure WAL Settings"],
    command: "ALTER SYSTEM SET wal_buffers = '16MB';",
    description: "Configure Write-Ahead Log buffer settings for better write performance.",
    parameters: [
      { flag: "wal_buffers", name: "wal buffers", description: "Memory for WAL data before writing to disk" },
      { flag: "16MB", name: "typical value", description: "Usually 3% of shared_buffers, up to 16MB" },
      { flag: "synchronous_commit", name: "synchronous commit", description: "Wait for WAL to be written to disk" },
      { flag: "wal_sync_method", name: "wal sync method", description: "Method for forcing WAL updates to disk" }
    ]
  },
  {
    path: ["Configuration Management", "Security Configuration", "Configure SSL"],
    command: "ALTER SYSTEM SET ssl = on;",
    description: "Enable SSL encryption for database connections.",
    parameters: [
      { flag: "ssl", name: "ssl enabled", description: "Enable SSL connections" },
      { flag: "ssl_cert_file", name: "certificate file", description: "Path to SSL certificate file" },
      { flag: "ssl_key_file", name: "key file", description: "Path to SSL private key file" },
      { flag: "ssl_ca_file", name: "ca file", description: "Path to certificate authority file" },
      { flag: "ssl_ciphers", name: "ssl ciphers", description: "Allowed SSL cipher suites" }
    ]
  },
  {
    path: ["Configuration Management", "Backup Configuration", "Configure Archive Mode"],
    command: "ALTER SYSTEM SET archive_mode = on;",
    description: "Enable WAL archiving for point-in-time recovery and replication.",
    parameters: [
      { flag: "archive_mode", name: "archive mode", description: "Enable WAL archiving (on, off, always)" },
      { flag: "archive_command", name: "archive command", description: "Command to archive completed WAL files" },
      { flag: "archive_timeout", name: "archive timeout", description: "Force WAL file switch after this time" },
      { flag: "wal_level", name: "wal level", description: "Amount of information in WAL (minimal, replica, logical)" }
    ]
  },
  {
    path: ["Configuration Management", "Query Optimization", "Configure Query Planner"],
    command: "ALTER SYSTEM SET random_page_cost = 1.1;",
    description: "Configure query planner cost parameters for better query optimization.",
    parameters: [
      { flag: "random_page_cost", name: "random page cost", description: "Cost of random disk page fetch (lower for SSD)" },
      { flag: "1.1", name: "ssd value", description: "Typical value for SSD storage" },
      { flag: "seq_page_cost", name: "sequential page cost", description: "Cost of sequential disk page fetch" },
      { flag: "cpu_tuple_cost", name: "cpu tuple cost", description: "Cost of processing each row" },
      { flag: "effective_cache_size", name: "effective cache size", description: "Planner's assumption about cache size" }
    ]
  },
  {
    path: ["Configuration Management", "Monitoring Configuration", "Configure Statistics"],
    command: "ALTER SYSTEM SET track_activities = on;",
    description: "Enable collection of statistics about database activity for monitoring.",
    parameters: [
      { flag: "track_activities", name: "track activities", description: "Collect information about executing commands" },
      { flag: "track_counts", name: "track counts", description: "Collect statistics on table and index accesses" },
      { flag: "track_io_timing", name: "track io timing", description: "Collect timing statistics for I/O operations" },
      { flag: "track_functions", name: "track functions", description: "Track function call statistics (none, pl, all)" },
      { flag: "log_statement_stats", name: "log statement stats", description: "Log performance statistics for each statement" }
    ]
  }
];

// Validation function for this module
export function validateConfigurationCommands() {
  return configurationCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Configuration Management"
  );
}
