// Managing Databases and Roles Commands
export const databaseCommands = [
  {
    path: ["Managing Databases and Roles", "Managing Databases", "List All Databases"],
    command: "\\l",
    description: "List all databases in your PostgreSQL cluster. Use \\l+ for more detailed information including database sizes."
  },
  {
    path: ["Managing Databases and Roles", "Managing Databases", "Create a Database (SQL)"],
    command: "CREATE DATABASE mydatabase;",
    description: "Create a new database named 'mydatabase'. You can specify additional options like the owner. Run this in psql after connecting to the postgres database."
  },
  {
    path: ["Managing Databases and Roles", "Managing Databases", "Delete a Database (SQL)"],
    command: "DROP DATABASE mydatabase;",
    description: "Permanently delete the database 'mydatabase' and all its contents. You must be connected to a different database (like postgres) to drop it."
  },
  {
    path: ["Managing Databases and Roles", "Managing Databases", "Connect to a Database"],
    command: "\\c mydatabase",
    description: "Switch to a different database within psql. This is a meta-command that doesn't require SQL syntax."
  },
  {
    path: ["Managing Databases and Roles", "Managing Databases", "Create a Database (Command Line)"],
    command: "createdb mydatabase",
    description: "Create a new database using the command-line utility. This is run directly from your Mac's terminal."
  },
  {
    path: ["Managing Databases and Roles", "Managing Databases", "Delete a Database (Command Line)"],
    command: "dropdb mydatabase",
    description: "Delete an existing database using the command-line utility. This is run directly from your Mac's terminal."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "List Users/Roles"],
    command: "\\du",
    description: "List all roles in your PostgreSQL cluster. Use \\du+ for more detailed information."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "Create a User/Role (SQL)"],
    command: "CREATE USER myuser WITH PASSWORD 'a_strong_password';",
    description: "Create a new user role with a password. This is the basic syntax for creating users."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "Create a User with Specific Privileges"],
    command: "CREATE USER anotheruser WITH PASSWORD 'a_strong_password' CREATEDB;",
    description: "Create a user who can create databases. You can also use SUPERUSER for full privileges (use with caution)."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "Alter a User/Role"],
    command: "ALTER USER myuser WITH SUPERUSER;",
    description: "Modify an existing user's privileges. Use NOSUPERUSER to remove superuser privileges."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "Delete a User/Role"],
    command: "DROP USER myuser;",
    description: "Remove a user role from the system."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "Create a User (Command Line)"],
    command: "createuser --interactive",
    description: "Create a new user using the command-line utility with an interactive prompt. Use --pwprompt to be prompted for a password."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "Delete a User (Command Line)"],
    command: "dropuser myuser",
    description: "Delete a user using the command-line utility."
  },
  {
    path: ["Managing Databases and Roles", "Managing Privileges", "Grant Privileges"],
    command: "GRANT CONNECT ON DATABASE mydatabase TO myuser;",
    description: "Allow a user to connect to a specific database. You can also grant privileges on tables or other objects."
  },
  {
    path: ["Managing Databases and Roles", "Managing Privileges", "Grant All Privileges on a Table"],
    command: "GRANT ALL ON mytable TO myuser;",
    description: "Give a user all privileges on a specific table. You can also grant specific privileges like SELECT, INSERT, UPDATE, DELETE."
  },
  {
    path: ["Managing Databases and Roles", "Managing Privileges", "Grant Usage on a Schema"],
    command: "GRANT USAGE ON SCHEMA public TO myuser;",
    description: "Allow a user to access objects within a schema. This is often needed before granting table privileges."
  },
  {
    path: ["Managing Databases and Roles", "Managing Privileges", "Grant Select on All Tables in Schema"],
    command: "GRANT SELECT ON ALL TABLES IN SCHEMA public TO myuser;",
    description: "Grant SELECT privileges on all existing tables in the public schema to a user."
  },
  {
    path: ["Managing Databases and Roles", "Managing Privileges", "Revoke Privileges"],
    command: "REVOKE DELETE ON mytable FROM myuser;",
    description: "Remove specific privileges from a user. This example revokes DELETE permission on a table."
  }
];

// Validation function for this module
export function validateDatabaseCommands() {
  return databaseCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Managing Databases and Roles"
  );
}