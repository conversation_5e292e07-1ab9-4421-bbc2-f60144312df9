// Managing Databases and Roles Commands
export const databaseCommands = [
  {
    path: ["Managing Databases and Roles", "Managing Databases", "List All Databases"],
    command: "\\l",
    description: "List all databases in your PostgreSQL cluster. Use \\l+ for more detailed information including database sizes.",
    parameters: [
      { flag: "\\l", name: "list databases", description: "Basic database listing with name, owner, encoding" },
      { flag: "\\l+", name: "detailed list", description: "Extended listing with size, tablespace, and description" },
      { flag: "\\l pattern", name: "pattern match", description: "List databases matching a pattern (e.g., \\l my*)" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Managing Databases", "Create a Database (SQL)"],
    command: "CREATE DATABASE mydatabase;",
    description: "Create a new database named 'mydatabase'. You can specify additional options like the owner. Run this in psql after connecting to the postgres database.",
    parameters: [
      { flag: "DATABASE name", name: "database name", description: "The name of the database to create" },
      { flag: "OWNER", name: "owner", description: "Database owner (e.g., OWNER = username)" },
      { flag: "TEMPLATE", name: "template", description: "Template database to copy from (default: template1)" },
      { flag: "ENCODING", name: "encoding", description: "Character encoding (e.g., ENCODING = 'UTF8')" },
      { flag: "TABLESPACE", name: "tablespace", description: "Default tablespace for the database" },
      { flag: "CONNECTION LIMIT", name: "connection limit", description: "Maximum concurrent connections (e.g., CONNECTION LIMIT = 100)" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Managing Databases", "Delete a Database (SQL)"],
    command: "DROP DATABASE mydatabase;",
    description: "Permanently delete the database 'mydatabase' and all its contents. You must be connected to a different database (like postgres) to drop it."
  },
  {
    path: ["Managing Databases and Roles", "Managing Databases", "Connect to a Database"],
    command: "\\c mydatabase",
    description: "Switch to a different database within psql. This is a meta-command that doesn't require SQL syntax."
  },
  {
    path: ["Managing Databases and Roles", "Managing Databases", "Create a Database (Command Line)"],
    command: "createdb mydatabase",
    description: "Create a new database using the command-line utility. This is run directly from your Mac's terminal.",
    parameters: [
      { flag: "mydatabase", name: "database name", description: "The name of the database to create" },
      { flag: "-U", name: "username", description: "PostgreSQL user to connect as" },
      { flag: "-h", name: "host", description: "Database server host (default: local socket)" },
      { flag: "-p", name: "port", description: "Database server port (default: 5432)" },
      { flag: "-O", name: "owner", description: "Database owner" },
      { flag: "-T", name: "template", description: "Template database to copy" },
      { flag: "-E", name: "encoding", description: "Character encoding for the database" },
      { flag: "-D", name: "tablespace", description: "Default tablespace for the database" },
      { flag: "-l", name: "locale", description: "Locale settings for the database" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Managing Databases", "Delete a Database (Command Line)"],
    command: "dropdb mydatabase",
    description: "Delete an existing database using the command-line utility. This is run directly from your Mac's terminal."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "List Users/Roles"],
    command: "\\du",
    description: "List all roles in your PostgreSQL cluster. Use \\du+ for more detailed information."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "Create a User/Role (SQL)"],
    command: "CREATE USER myuser WITH PASSWORD 'a_strong_password';",
    description: "Create a new user role with a password. This is the basic syntax for creating users."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "Create a User with Specific Privileges"],
    command: "CREATE USER anotheruser WITH PASSWORD 'a_strong_password' CREATEDB;",
    description: "Create a user who can create databases. You can also use SUPERUSER for full privileges (use with caution)."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "Alter a User/Role"],
    command: "ALTER USER myuser WITH SUPERUSER;",
    description: "Modify an existing user's privileges. Use NOSUPERUSER to remove superuser privileges."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "Delete a User/Role"],
    command: "DROP USER myuser;",
    description: "Remove a user role from the system."
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "Create a User (Command Line)"],
    command: "createuser --interactive",
    description: "Create a new user using the command-line utility with an interactive prompt. Use --pwprompt to be prompted for a password.",
    parameters: [
      { flag: "--interactive", name: "interactive", description: "Prompt for user name and attributes interactively" },
      { flag: "--pwprompt", name: "password prompt", description: "Prompt for a password for the new user" },
      { flag: "-U", name: "username", description: "PostgreSQL user to connect as (must have CREATEROLE privilege)" },
      { flag: "-h", name: "host", description: "Database server host (default: local socket)" },
      { flag: "-p", name: "port", description: "Database server port (default: 5432)" },
      { flag: "-s", name: "superuser", description: "Make the new user a superuser" },
      { flag: "-S", name: "no-superuser", description: "Make the new user not a superuser (default)" },
      { flag: "-d", name: "createdb", description: "Allow the new user to create databases" },
      { flag: "-D", name: "no-createdb", description: "Do not allow the new user to create databases (default)" },
      { flag: "-r", name: "createrole", description: "Allow the new user to create roles" },
      { flag: "-R", name: "no-createrole", description: "Do not allow the new user to create roles (default)" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Managing Users/Roles", "Delete a User (Command Line)"],
    command: "dropuser myuser",
    description: "Delete a user using the command-line utility."
  },
  {
    path: ["Managing Databases and Roles", "Managing Privileges", "Grant Privileges"],
    command: "GRANT CONNECT ON DATABASE mydatabase TO myuser;",
    description: "Allow a user to connect to a specific database. You can also grant privileges on tables or other objects."
  },
  {
    path: ["Managing Databases and Roles", "Managing Privileges", "Grant All Privileges on a Table"],
    command: "GRANT ALL ON mytable TO myuser;",
    description: "Give a user all privileges on a specific table. You can also grant specific privileges like SELECT, INSERT, UPDATE, DELETE."
  },
  {
    path: ["Managing Databases and Roles", "Managing Privileges", "Grant Usage on a Schema"],
    command: "GRANT USAGE ON SCHEMA public TO myuser;",
    description: "Allow a user to access objects within a schema. This is often needed before granting table privileges."
  },
  {
    path: ["Managing Databases and Roles", "Managing Privileges", "Grant Select on All Tables in Schema"],
    command: "GRANT SELECT ON ALL TABLES IN SCHEMA public TO myuser;",
    description: "Grant SELECT privileges on all existing tables in the public schema to a user."
  },
  {
    path: ["Managing Databases and Roles", "Managing Privileges", "Revoke Privileges"],
    command: "REVOKE DELETE ON mytable FROM myuser;",
    description: "Remove specific privileges from a user. This example revokes DELETE permission on a table.",
    parameters: [
      { flag: "DELETE", name: "privilege", description: "The privilege to revoke (SELECT, INSERT, UPDATE, DELETE, ALL, etc.)" },
      { flag: "ON mytable", name: "object", description: "The database object (table, schema, database, etc.)" },
      { flag: "FROM myuser", name: "user", description: "The user or role to revoke privileges from" },
      { flag: "CASCADE", name: "cascade", description: "Also revoke from users who received privileges from this user" },
      { flag: "RESTRICT", name: "restrict", description: "Refuse to revoke if other users depend on these privileges" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Advanced Database Management", "Alter Database Settings"],
    command: "ALTER DATABASE mydatabase SET timezone = 'UTC';",
    description: "Modify database-specific configuration parameters that apply to all sessions in this database.",
    parameters: [
      { flag: "ALTER DATABASE", name: "alter database", description: "Command to modify database properties" },
      { flag: "mydatabase", name: "database name", description: "Name of the database to modify" },
      { flag: "SET parameter", name: "set parameter", description: "Set a configuration parameter (e.g., timezone, work_mem)" },
      { flag: "RESET parameter", name: "reset parameter", description: "Reset a parameter to its default value" },
      { flag: "RENAME TO", name: "rename", description: "Rename the database (e.g., RENAME TO new_name)" },
      { flag: "OWNER TO", name: "change owner", description: "Change database owner (e.g., OWNER TO new_owner)" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Advanced Database Management", "Database Size and Statistics"],
    command: "SELECT datname, pg_size_pretty(pg_database_size(datname)) FROM pg_database;",
    description: "Get the size of all databases in the cluster with human-readable formatting.",
    parameters: [
      { flag: "datname", name: "database name", description: "Column containing database names from pg_database" },
      { flag: "pg_database_size()", name: "size function", description: "Function that returns database size in bytes" },
      { flag: "pg_size_pretty()", name: "format function", description: "Function that formats bytes into human-readable format" },
      { flag: "pg_database", name: "system catalog", description: "System table containing database information" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Advanced User Management", "Role Inheritance and Groups"],
    command: "CREATE ROLE developers; GRANT developers TO alice, bob;",
    description: "Create a group role and assign users to it for easier privilege management.",
    parameters: [
      { flag: "CREATE ROLE", name: "create role", description: "Create a new role (similar to user but cannot login by default)" },
      { flag: "developers", name: "role name", description: "Name of the group role" },
      { flag: "GRANT role TO", name: "grant membership", description: "Add users to the role group" },
      { flag: "LOGIN", name: "login privilege", description: "Allow the role to login (use for users)" },
      { flag: "NOLOGIN", name: "no login", description: "Prevent the role from logging in (default for roles)" },
      { flag: "INHERIT", name: "inherit", description: "Role inherits privileges of roles it's a member of (default)" },
      { flag: "NOINHERIT", name: "no inherit", description: "Role does not inherit privileges from member roles" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Advanced User Management", "Row Level Security"],
    command: "ALTER TABLE sensitive_data ENABLE ROW LEVEL SECURITY;",
    description: "Enable row-level security on a table to control which rows users can see or modify.",
    parameters: [
      { flag: "ALTER TABLE", name: "alter table", description: "Command to modify table properties" },
      { flag: "ENABLE ROW LEVEL SECURITY", name: "enable RLS", description: "Turn on row-level security for the table" },
      { flag: "DISABLE ROW LEVEL SECURITY", name: "disable RLS", description: "Turn off row-level security" },
      { flag: "FORCE ROW LEVEL SECURITY", name: "force RLS", description: "Apply RLS even to table owners and superusers" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Advanced User Management", "Create RLS Policy"],
    command: "CREATE POLICY user_data ON users FOR ALL TO app_user USING (user_id = current_user_id());",
    description: "Create a row-level security policy to restrict data access based on conditions.",
    parameters: [
      { flag: "CREATE POLICY", name: "create policy", description: "Command to create a new RLS policy" },
      { flag: "user_data", name: "policy name", description: "Name for the policy" },
      { flag: "ON users", name: "table", description: "Table the policy applies to" },
      { flag: "FOR ALL", name: "command", description: "Commands the policy applies to (ALL, SELECT, INSERT, UPDATE, DELETE)" },
      { flag: "TO app_user", name: "role", description: "Role(s) the policy applies to" },
      { flag: "USING (condition)", name: "using clause", description: "Condition that must be true for row visibility" },
      { flag: "WITH CHECK (condition)", name: "with check", description: "Condition for INSERT/UPDATE operations" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Tablespace Management", "List Tablespaces"],
    command: "\\db",
    description: "List all tablespaces in the PostgreSQL cluster. Use \\db+ for detailed information.",
    parameters: [
      { flag: "\\db", name: "list tablespaces", description: "Basic tablespace listing" },
      { flag: "\\db+", name: "detailed list", description: "Extended listing with location and options" },
      { flag: "\\db pattern", name: "pattern match", description: "List tablespaces matching a pattern" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Tablespace Management", "Create Tablespace"],
    command: "CREATE TABLESPACE fast_storage LOCATION '/path/to/fast/storage';",
    description: "Create a new tablespace to store database objects in a specific location on disk.",
    parameters: [
      { flag: "CREATE TABLESPACE", name: "create tablespace", description: "Command to create a new tablespace" },
      { flag: "fast_storage", name: "tablespace name", description: "Name for the new tablespace" },
      { flag: "LOCATION", name: "location", description: "File system path where tablespace files will be stored" },
      { flag: "OWNER", name: "owner", description: "User who owns the tablespace (default: current user)" },
      { flag: "WITH", name: "options", description: "Tablespace options (e.g., WITH (random_page_cost=1.0))" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Tablespace Management", "Move Table to Tablespace"],
    command: "ALTER TABLE mytable SET TABLESPACE fast_storage;",
    description: "Move an existing table to a different tablespace for performance or storage management.",
    parameters: [
      { flag: "ALTER TABLE", name: "alter table", description: "Command to modify table properties" },
      { flag: "mytable", name: "table name", description: "Name of the table to move" },
      { flag: "SET TABLESPACE", name: "set tablespace", description: "Specify the target tablespace" },
      { flag: "fast_storage", name: "tablespace name", description: "Name of the destination tablespace" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Connection Management", "Show Current Connections"],
    command: "SELECT usename, datname, client_addr, state, query_start FROM pg_stat_activity WHERE state = 'active';",
    description: "Display currently active database connections and their details.",
    parameters: [
      { flag: "usename", name: "username", description: "Name of the user connected to the database" },
      { flag: "datname", name: "database name", description: "Name of the database being accessed" },
      { flag: "client_addr", name: "client address", description: "IP address of the client connection" },
      { flag: "state", name: "connection state", description: "Current state of the connection (active, idle, etc.)" },
      { flag: "query_start", name: "query start time", description: "When the current query started" },
      { flag: "WHERE state = 'active'", name: "filter", description: "Filter to show only active connections" }
    ]
  },
  {
    path: ["Managing Databases and Roles", "Connection Management", "Kill Connection"],
    command: "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE usename = 'problem_user';",
    description: "Terminate database connections for a specific user or based on other criteria.",
    parameters: [
      { flag: "pg_terminate_backend()", name: "terminate function", description: "Function to forcefully terminate a backend process" },
      { flag: "pid", name: "process id", description: "Process ID of the connection to terminate" },
      { flag: "WHERE usename", name: "filter by user", description: "Filter connections by username" },
      { flag: "WHERE datname", name: "filter by database", description: "Filter connections by database name" },
      { flag: "WHERE state", name: "filter by state", description: "Filter connections by state (idle, active, etc.)" }
    ]
  }
];

// Validation function for this module
export function validateDatabaseCommands() {
  return databaseCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Managing Databases and Roles"
  );
}