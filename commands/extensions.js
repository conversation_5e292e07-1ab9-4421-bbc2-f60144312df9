// Extensions and Advanced Features Commands
export const extensionsCommands = [
  {
    path: ["Extensions and Advanced Features", "Extension Management", "List Available Extensions"],
    command: "SELECT name, default_version, comment FROM pg_available_extensions ORDER BY name;",
    description: "Display all extensions available for installation in the current PostgreSQL instance.",
    parameters: [
      { flag: "name", name: "extension name", description: "Name of the extension" },
      { flag: "default_version", name: "default version", description: "Default version that will be installed" },
      { flag: "comment", name: "description", description: "Brief description of the extension" },
      { flag: "installed_version", name: "installed version", description: "Currently installed version (if any)" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "Extension Management", "List Installed Extensions"],
    command: "\\dx",
    description: "List all currently installed extensions in the database. Use \\dx+ for detailed information.",
    parameters: [
      { flag: "\\dx", name: "list extensions", description: "Basic extension listing" },
      { flag: "\\dx+", name: "detailed list", description: "Extended listing with version and schema information" },
      { flag: "\\dx extension_name", name: "specific extension", description: "Show details for a specific extension" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "Extension Management", "Install Extension"],
    command: "CREATE EXTENSION IF NOT EXISTS postgis;",
    description: "Install an extension in the current database. Use IF NOT EXISTS to avoid errors if already installed.",
    parameters: [
      { flag: "CREATE EXTENSION", name: "create extension", description: "Install a new extension" },
      { flag: "IF NOT EXISTS", name: "if not exists", description: "Don't error if extension already exists" },
      { flag: "postgis", name: "extension name", description: "Name of the extension to install" },
      { flag: "WITH SCHEMA", name: "schema", description: "Install extension in specific schema" },
      { flag: "VERSION", name: "version", description: "Install specific version of extension" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "PostGIS (Spatial Data)", "Enable PostGIS"],
    command: "CREATE EXTENSION IF NOT EXISTS postgis; CREATE EXTENSION IF NOT EXISTS postgis_topology;",
    description: "Install PostGIS extension for spatial and geographic data support.",
    parameters: [
      { flag: "postgis", name: "postgis core", description: "Core PostGIS extension for spatial data types" },
      { flag: "postgis_topology", name: "topology", description: "Topology support for PostGIS" },
      { flag: "postgis_raster", name: "raster", description: "Raster data support" },
      { flag: "postgis_sfcgal", name: "sfcgal", description: "3D geometry support" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "PostGIS (Spatial Data)", "Create Spatial Table"],
    command: "CREATE TABLE locations (id SERIAL PRIMARY KEY, name TEXT, geom GEOMETRY(POINT, 4326));",
    description: "Create a table with spatial geometry column using PostGIS data types.",
    parameters: [
      { flag: "GEOMETRY", name: "geometry type", description: "PostGIS geometry data type" },
      { flag: "POINT", name: "geometry subtype", description: "Specific geometry type (POINT, LINESTRING, POLYGON)" },
      { flag: "4326", name: "SRID", description: "Spatial Reference System Identifier (4326 = WGS84)" },
      { flag: "GEOGRAPHY", name: "geography type", description: "Alternative to GEOMETRY for spherical calculations" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "PostGIS (Spatial Data)", "Spatial Queries"],
    command: "SELECT name FROM locations WHERE ST_DWithin(geom, ST_MakePoint(-122.4194, 37.7749), 1000);",
    description: "Find all locations within 1000 meters of a specific point using PostGIS spatial functions.",
    parameters: [
      { flag: "ST_DWithin()", name: "distance within", description: "Check if geometries are within specified distance" },
      { flag: "ST_MakePoint()", name: "make point", description: "Create a point geometry from coordinates" },
      { flag: "ST_Distance()", name: "distance", description: "Calculate distance between geometries" },
      { flag: "ST_Contains()", name: "contains", description: "Check if one geometry contains another" },
      { flag: "ST_Intersects()", name: "intersects", description: "Check if geometries intersect" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "Full-Text Search", "Create Text Search Configuration"],
    command: "CREATE TEXT SEARCH CONFIGURATION my_config (COPY = english);",
    description: "Create a custom text search configuration for full-text search functionality.",
    parameters: [
      { flag: "CREATE TEXT SEARCH CONFIGURATION", name: "create config", description: "Create new text search configuration" },
      { flag: "my_config", name: "config name", description: "Name for the configuration" },
      { flag: "COPY = english", name: "copy from", description: "Base configuration to copy from" },
      { flag: "ALTER TEXT SEARCH CONFIGURATION", name: "alter config", description: "Modify existing configuration" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "Full-Text Search", "Create Full-Text Index"],
    command: "CREATE INDEX idx_documents_fts ON documents USING GIN(to_tsvector('english', title || ' ' || content));",
    description: "Create a GIN index for efficient full-text search on document content.",
    parameters: [
      { flag: "GIN", name: "gin index", description: "Generalized Inverted Index for full-text search" },
      { flag: "to_tsvector()", name: "to tsvector", description: "Convert text to tsvector for indexing" },
      { flag: "'english'", name: "language config", description: "Text search configuration to use" },
      { flag: "||", name: "concatenation", description: "Concatenate multiple text fields" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "Full-Text Search", "Full-Text Search Query"],
    command: "SELECT title FROM documents WHERE to_tsvector('english', title || ' ' || content) @@ to_tsquery('english', 'postgresql & performance');",
    description: "Perform full-text search using PostgreSQL's built-in search capabilities.",
    parameters: [
      { flag: "@@", name: "match operator", description: "Full-text search match operator" },
      { flag: "to_tsquery()", name: "to tsquery", description: "Convert search terms to tsquery" },
      { flag: "&", name: "and operator", description: "Both terms must be present" },
      { flag: "|", name: "or operator", description: "Either term can be present" },
      { flag: "!", name: "not operator", description: "Term must not be present" },
      { flag: "ts_rank()", name: "ranking", description: "Rank search results by relevance" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "UUID and Crypto", "Install UUID Extension"],
    command: "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";",
    description: "Install UUID extension for generating universally unique identifiers.",
    parameters: [
      { flag: "uuid-ossp", name: "uuid extension", description: "Extension providing UUID generation functions" },
      { flag: "uuid_generate_v1()", name: "uuid v1", description: "Generate time-based UUID" },
      { flag: "uuid_generate_v4()", name: "uuid v4", description: "Generate random UUID" },
      { flag: "gen_random_uuid()", name: "random uuid", description: "Built-in function (PostgreSQL 13+)" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "UUID and Crypto", "Install Crypto Extension"],
    command: "CREATE EXTENSION IF NOT EXISTS pgcrypto;",
    description: "Install pgcrypto extension for cryptographic functions and data encryption.",
    parameters: [
      { flag: "pgcrypto", name: "crypto extension", description: "Extension providing cryptographic functions" },
      { flag: "crypt()", name: "password hashing", description: "Hash passwords with various algorithms" },
      { flag: "gen_salt()", name: "salt generation", description: "Generate salt for password hashing" },
      { flag: "encrypt()", name: "encryption", description: "Encrypt data with various algorithms" },
      { flag: "decrypt()", name: "decryption", description: "Decrypt previously encrypted data" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "Foreign Data Wrappers", "Install postgres_fdw"],
    command: "CREATE EXTENSION IF NOT EXISTS postgres_fdw;",
    description: "Install Foreign Data Wrapper for accessing remote PostgreSQL databases.",
    parameters: [
      { flag: "postgres_fdw", name: "postgres fdw", description: "Foreign data wrapper for PostgreSQL" },
      { flag: "CREATE SERVER", name: "create server", description: "Define remote server connection" },
      { flag: "CREATE USER MAPPING", name: "user mapping", description: "Map local users to remote users" },
      { flag: "CREATE FOREIGN TABLE", name: "foreign table", description: "Create table that references remote data" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "Foreign Data Wrappers", "Create Foreign Server"],
    command: "CREATE SERVER remote_db FOREIGN DATA WRAPPER postgres_fdw OPTIONS (host 'remote-host', dbname 'remote_database', port '5432');",
    description: "Create a foreign server definition to connect to a remote PostgreSQL database.",
    parameters: [
      { flag: "CREATE SERVER", name: "create server", description: "Define a foreign server" },
      { flag: "remote_db", name: "server name", description: "Local name for the remote server" },
      { flag: "FOREIGN DATA WRAPPER", name: "fdw", description: "Specify which FDW to use" },
      { flag: "OPTIONS", name: "connection options", description: "Connection parameters for the remote server" },
      { flag: "host", name: "hostname", description: "Remote server hostname or IP" },
      { flag: "dbname", name: "database name", description: "Remote database name" },
      { flag: "port", name: "port number", description: "Remote server port" }
    ]
  },
  {
    path: ["Extensions and Advanced Features", "Time Series Data", "Install TimescaleDB"],
    command: "CREATE EXTENSION IF NOT EXISTS timescaledb;",
    description: "Install TimescaleDB extension for time-series data optimization (requires separate installation).",
    parameters: [
      { flag: "timescaledb", name: "timescaledb", description: "Time-series database extension" },
      { flag: "CREATE_HYPERTABLE()", name: "create hypertable", description: "Convert regular table to hypertable" },
      { flag: "time_bucket()", name: "time bucket", description: "Group time-series data into intervals" },
      { flag: "first()", name: "first function", description: "Get first value in time order" },
      { flag: "last()", name: "last function", description: "Get last value in time order" }
    ]
  }
];

// Validation function for this module
export function validateExtensionsCommands() {
  return extensionsCommands.every(cmd =>
    cmd.path &&
    cmd.path.length > 0 &&
    cmd.command &&
    cmd.description &&
    cmd.path[0] === "Extensions and Advanced Features"
  );
}
